/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArcProgress: typeof import('./src/components/ui/ArcProgress.vue')['default']
    CardWithTitle: typeof import('./src/components/ui/CardWithTitle/CardWithTitle.vue')['default']
    CheckMark: typeof import('./src/components/ui/PureNotify/components/CheckMark.vue')['default']
    ComingSoon: typeof import('./src/components/ui/ComingSoon/ComingSoon.vue')['default']
    CrossMark: typeof import('./src/components/ui/PureNotify/components/CrossMark.vue')['default']
    DashboardCard001: typeof import('./src/components/ui/DashboardCard001/DashboardCard001.vue')['default']
    DashboardCard002: typeof import('./src/components/ui/DashboardCard002/DashboardCard002.vue')['default']
    DashboardCard003: typeof import('./src/components/ui/DashboardCard003/DashboardCard003.vue')['default']
    DashboardCard004: typeof import('./src/components/ui/DashboardCard004/DashboardCard004.vue')['default']
    DashboardCard005: typeof import('./src/components/ui/DashboardCard005/DashboardCard005.vue')['default']
    DashboardCard006: typeof import('./src/components/ui/DashboardCard006/DashboardCard006.vue')['default']
    DashboardCard007: typeof import('./src/components/ui/DashboardCard007/DashboardCard007.vue')['default']
    DashboardCard008: typeof import('./src/components/ui/DashboardCard008/DashboardCard008.vue')['default']
    DmpBarStackChart: typeof import('@dmp/components')['DmpBarStackChart']
    DmpCycleSelector: typeof import('@dmp/components')['DmpCycleSelector']
    DmpDetailTable: typeof import('@dmp/components')['DmpDetailTable']
    DmpGlassCard: typeof import('@dmp/components')['DmpGlassCard']
    DmpInfoDetailTable: typeof import('@dmp/components')['DmpInfoDetailTable']
    DmpLineChart: typeof import('@dmp/components')['DmpLineChart']
    DmpLottiePlayer: typeof import('@dmp/components')['DmpLottiePlayer']
    DmpPopoverProvider: typeof import('@dmp/components')['DmpPopoverProvider']
    DmpPopoverRank: typeof import('@dmp/components')['DmpPopoverRank']
    DmpSidebar: typeof import('@dmp/components')['DmpSidebar']
    DmpSidebarMobile: typeof import('@dmp/components')['DmpSidebarMobile']
    DmpSimpleDropdown: typeof import('@dmp/components')['DmpSimpleDropdown']
    DmpTabSwitcher: typeof import('@dmp/components')['DmpTabSwitcher']
    DmpTextList: typeof import('@dmp/components')['DmpTextList']
    DmpValueWithUnit: typeof import('@dmp/components')['DmpValueWithUnit']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    GlassCard: typeof import('./src/components/ui/GlassCard.vue')['default']
    InfoMark: typeof import('./src/components/ui/PureNotify/components/InfoMark.vue')['default']
    ItemWithArrow: typeof import('./src/components/ui/ItemWithArrow/ItemWithArrow.vue')['default']
    PureNotify: typeof import('./src/components/ui/PureNotify/PureNotify.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabSwitcher: typeof import('./src/components/ui/TabSwitcher.vue')['default']
    ValueWithUnit: typeof import('./src/components/ui/ValueWithUnit/ValueWithUnit.vue')['default']
  }
}
