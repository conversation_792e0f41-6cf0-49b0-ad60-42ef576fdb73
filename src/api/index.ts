/*
 * @Date: 2025-07-21 14:25:29
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-24 16:20:37
 * @FilePath: /MyBusinessV2/src/api/index.ts
 */
import { get, request } from "@/utils/http.ts";

import { ProductRankDialogResponse, ProductRankDialogRequest } from "./interface/ProductRankDialog";
import {
  Config,
  ConfigParams,
  FiscalWeeksResponse,
  UpdateTimeResponse,
  FiscalDaysResponse,
} from "./interface/Config";
import {
  InventoryWarningDialogResponse,
  InventoryWarningTrendResponse,
} from "./interface/InventoryWarningDialog";
import { InventoryRemindResponse } from "./interface/InventoryRemind";
import { SalesWarningResponse, SalesWarningTrendItem } from "./interface/SalesWarning";
import { StoreRankDialogResponse } from "./interface/StoreRankDialog";
import {
  SalesPerformanceDialogResponse,
  SalesEchartDialogResponse,
} from "./interface/SalesPerformanceDialog";
import {
  InventoryPerformanceDialogResponse,
  InventoryEchartDialogResponse,
} from "./interface/InventoryPerformanceDialog";
import { HealthDiagnosisDialogResponse } from "./interface/HealthDiagnosisDialog";
import { SchemaResponse } from "@/api/interface/Schema.ts";
import { TreadeInDialogResponse, TradeInTrendResponse } from "./interface/TradeInDialog";

// 获取用户信息
export const getUserInfo = () => {
  return get("/user/info");
};

// 获取reseller信息
export const getResellerInfo = () => {
  return get("/reseller/info");
};

// 获取 Schema 配置
export const getSchema = () => {
  return get<SchemaResponse>("/display/config", { project: "mybiz", page: "dashboard" });
};

// 获取fiscal weeks
export const getFiscalWeeks = () => {
  return get<FiscalWeeksResponse>("/hk_tw/recent/weeks");
};

export const getUpdateTime = () => {
  return get<UpdateTimeResponse>("/hk_tw/datatime");
};

export const getFiscalDays = () => {
  return get<FiscalDaysResponse>("/hk_tw/recent/day");
};

// 获取 销售表现中间态
export const getSalesPerformanceDialogInfo = (params) => {
  return get<SalesPerformanceDialogResponse>("/hk_tw/datacenter/statistics", params);
};
// 获取 销售表现趋势图
export const getSalesEchartDialogInfo = (params) => {
  return get<SalesEchartDialogResponse>("/hk_tw/datacenter/sales/inter_trend", params);
};
export const getInventoryDialogInfo = (params) => {
  return get<InventoryPerformanceDialogResponse>("/hk_tw/datacenter/inventory/details", params);
};

export const getInventoryEchartDialogInfo = (params) => {
  return get<InventoryEchartDialogResponse>("/hk_tw/datacenter/inventory/distribution", params);
};

// 产品热销排行榜 中间态
export const getProductRankDialogInfo = (params: ProductRankDialogRequest) => {
  return get<ProductRankDialogResponse>("/hk_tw/product_rank/inter_rank", params);
};

// 健康诊断 中间态
export const getScoreDetailsDialogInfo = (params) => {
  return get<HealthDiagnosisDialogResponse>("/hk_tw/health/score_details", params);
};
export const getStoreRankDialogInfo = (params) => {
  return get<StoreRankDialogResponse>("/hk_tw/store_rank/inter_rank", params);
};
//销售预警 预警分布
export const getSalesAlertDialogInfo = (params) => {
  return get<SalesWarningResponse>("/hk_tw/health/so_warning/inventory_distribution", params);
};

//销售预警 健康度趋势
export const getSalesAlertTrendInfo = (params) => {
  return get<SalesWarningTrendItem>("/hk_tw/health/so_warning/health_trend", params);
};

//库存提醒
export const getInventoryRemainderInfo = (params) => {
  return get<InventoryRemindResponse>("/hk_tw/health/inv_remind/inventory_distribution", params);
};

//库存预警
export const getInventoryWarningInfo = (params) => {
  return get<InventoryWarningDialogResponse>(
    "/hk_tw/health/inv_warning/inventory_distribution",
    params
  );
};

// 库存预警 折线图
export const getInventoryWarningTrend = (params) => {
  return get<InventoryWarningTrendResponse>("/hk_tw/health/inv_warning/health_trend", params);
};

//以舊換新
export const getTradeInInfo = (params) => {
  return get<TreadeInDialogResponse>("/hk_tw/datacenter/retail/statistics", params);
};

//  以舊換新趋势
export const getDatacenterTrendInfo = (params) => {
  return get<TradeInTrendResponse>("/hk_tw/datacenter/retail/trend", params);
};

// 获取显示配置（默认tabs等配置信息）
export const getDisplayConfig = (params: ConfigParams) => {
  return get<Config>("/display/config", params);
};

//销售预警下载
export const getSalesWarningDownload = (params) => {
  return request({
    url: "/hk_tw/health/so_warning/download",
    method: "get",
    params,
    responseType: "blob",
  });
};

//库存预警下载
export const getInventoryWarningDownload = (params) => {
  return request({
    url: "/hk_tw/health/inv_warning/download",
    method: "get",
    params,
    responseType: "blob",
  });
};

// 库存提醒下载
export const getInventoryRemainderDownload = (params) => {
  return request({
    url: "/hk_tw/health/inv_remind/download",
    method: "get",
    params,
    responseType: "blob",
  });
};
// 销售表现下载
export const getSalesPerformanceDownload = (params) => {
  return request({
    url: "/hk_tw/datacenter/export",
    method: "get",
    params,
    responseType: "blob",
  });
};

// 库存表现下载
export const getInventoryPerformanceDownload = (params) => {
  return request({
    url: "/hk_tw/datacenter/inventory/export",
    method: "get",
    params,
    responseType: "blob",
  });
};

// 以舊換新下载
export const getTradeInDownload = (params) => {
  return request({
    url: "/hk_tw/datacenter/retail/export",
    method: "get",
    params,
    responseType: "blob",
  });
};

// 门店排名下载
export const getStoreRankDownload = (params) => {
  return request({
    url: "/hk_tw/store_rank/export",
    method: "get",
    params,
    responseType: "blob",
  });
};
