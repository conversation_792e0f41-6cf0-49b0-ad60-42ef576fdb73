export interface SalesWarningItem {
  lob: string;
  levels: SalesWarningLevelsItem[];
}

export interface SalesWarningLevelsItem {
  level: string;
  warn_cnt: number;
  pos_cnt: number;
}

export interface SalesWarningResponse {
  date: string;
  warning_lobs: SalesWarningItem[];
  inv_remind_lobs: SalesWarningItem[];
}

export interface SalesWarningTrendItem {
  fiscal_qtr_week_name: string;
  medium: number;
  severe: number;
  very_severe: number;
  week_name: string;
}
