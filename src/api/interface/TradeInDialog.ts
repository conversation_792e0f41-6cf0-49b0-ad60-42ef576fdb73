export type TreadeInDialogResponse = Array<TreadeInDetail>;

export interface TreadeInDetail {
  fiscal_qtr_week_name: string;
  statisticsList: StatisticsInfo[];
}

export interface StatisticsInfo {
  name: string;
  value: number;
  value_type: string;
}

export type TradeInTrendResponse = Array<TradeInTrendItem>;

export interface TradeInTrendItem {
  fiscal_qtr_week_name: string;
  count_list: TradeInTrendData[];
  proportion_list: TradeInTrendData[];
}

export interface TradeInTrendData {
  fiscal_week: string;
  value: number;
}
