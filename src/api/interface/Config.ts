export interface Config {
  id: number;
  rtm: string;
  sub_rtm: string;
  reseller_type: string;
  reseller_id: string;
  page: string;
  config: string;
  project: string;
}

export interface ConfigParams {
  project: string;
  page: string;
}

export type FiscalWeeksResponse = Array<FiscalWeeksItem>;

export interface FiscalWeeksItem {
  id: number;
  fiscal_dt: string;
  week_begin_dt: string;
  week_end_dt: string;
  fiscal_year: number;
  fiscal_quarter: string;
  fiscal_week: string;
  fiscal_week_year: number;
  day_in_fiscal_week: number;
  fiscal_qtr_week_name: string;
  fiscal_qtr_year_name: string;
  week_date: string;
  week_in_fiscal_quarter: string;
  quarter_begin_dt: string;
}

export type UpdateTimeResponse = Array<UpdateTimeItem>;

export interface UpdateTimeItem {
  module_name: string;
  fiscal_week_year?: string | number;
  fiscal_dt?: string;
}

export type FiscalDaysResponse = Array<FiscalDaysItem>;

export interface FiscalDaysItem {
  id: number;
  fiscal_dt: string;
  week_begin_dt: string;
  week_end_dt: string;
  fiscal_year: number;
  fiscal_quarter: string;
  fiscal_week: string;
  fiscal_week_year: number;
  day_in_fiscal_week: number;
  fiscal_qtr_week_name: string;
  fiscal_qtr_year_name: string;
  week_date: string;
  week_in_fiscal_quarter: string;
  quarter_begin_dt: string;
}
