export type SalesPerformanceDialogResponse = Array<SalesPerformanceDialogItem>;

export interface SalesPerformanceDialogItem {
  fiscal_qtr_week_name: string;
  metrics: Array<SalesPerformanceDialogMetric>;
}

export interface SalesPerformanceDialogMetric {
  name: string;
  value: number | string;
  value_type: string;
  unit?: string;
  trend?: number;
}

export type SalesEchartDialogResponse = Array<SalesEchartDialogItem>;

export interface SalesEchartDialogItem {
  fiscal_week: string;
  so: number;
  fiscal_qtr_week?: string;
}
