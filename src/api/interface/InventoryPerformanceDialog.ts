export type InventoryPerformanceDialogResponse = Array<InventoryPerformanceDialogItem>;

export interface InventoryPerformanceDialogItem {
  fiscal_qtr_week_name: string;
  inv_data: Array<InventoryPerformanceDialogMetric>;
}

export interface InventoryPerformanceDialogMetric {
  name: string;
  value: number | string;
  value_type: string;
  unit?: string;
}

export type InventoryEchartDialogResponse = Array<InventoryEchartDialogItem>;

export interface InventoryEchartDialogItem {
  fiscal_qtr_week_name: string;
  datas: Array<InventoryEchartDialogMetric>;
}

export interface InventoryEchartDialogMetric {
  sub_lob: string;
  warehouse_inv: number;
  pos_inv: number;
  fiscal_qtr_week_name?: string;
  fiscal_week_year?: string | number;
  lob?: string;
}
