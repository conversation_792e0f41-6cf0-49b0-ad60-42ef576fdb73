export interface InventoryWarningDialogLevelsItem {
  level: string;
  warn_cnt: number;
  pos_cnt: number;
}

export interface InventoryWarningDialogItem {
  lob: string;
  levels: InventoryWarningDialogLevelsItem[];
}

export interface InventoryWarningDialogResponse {
  date: string;
  warning_lobs: InventoryWarningDialogItem[];
  inv_remind_lobs: InventoryWarningDialogItem[];
}

export interface InventoryWarningTrendResponse {
  fiscal_qtr_week_name: string;
  very_severe: number;
  severe: number;
  medium: number;
  week_name: string;
}
