// 健康诊断 API 响应类型
export interface HealthDiagnosisResponse {
  summary: {
    score: number;
    level: string;
  };
  metrics: HealthMetric[];
}

export interface HealthMetric {
  max_warning_cnt: number;
  warning_cnt: number;
  name: string;
  cn_name: string;
  level: string;
}

// 组件使用的格式化后的类型
export interface FormattedHealthDiagnosis {
  // 健康度分数
  progress: number;
  // 状态文本
  statusText: string;
  // 健康提示
  healthTips: string[];
  // 警报状态项
  alertItems: FormattedAlertItem[];
  // 健康度等级
  level: string;
}

export interface FormattedAlertItem {
  id: string;
  title: string;
  progress: number;
  warningCnt: number | string;
  color: string;
  statusText: string;
  gradient?: string[];
  statusLevel: "normal" | "warning" | "good" | "qualified";
  showText: boolean;
  progressUnit: string;
  textColor: string;
}
