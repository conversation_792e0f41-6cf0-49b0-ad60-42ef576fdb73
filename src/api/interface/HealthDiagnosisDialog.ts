export type HealthDiagnosisDialogResponse = Array<HealthDiagnosisDialogItem>;

export interface HealthDiagnosisDialogItem {
  fiscal_qtr_week_name: string;
  metrics: Array<HealthDiagnosisDialogMetric>;
  inv_remind_week_trend?: Array<HealthDiagnosisDialogMetric>;
  inv_warning_week_trend?: Array<HealthDiagnosisDialogMetric>;
  so_warning_week_trend?: Array<HealthDiagnosisDialogMetric>;
  total_week_trend?: Array<HealthDiagnosisDialogMetric>;
}

export interface HealthDiagnosisDialogMetric {
  name: string;
  total_score: number;
  score: number;
  cn_name?: string;
  fiscal_qtr_week_name?: string;
  week_name?: string;
}
