export type SalesPerformanceResponse = Array<SalesPerformanceItem>;

export interface SalesPerformanceItem {
  lob: string;
  weekly: number;
  quarter: number;
}

export type InventorySummaryResponse = Array<InventorySummaryItem>;

export interface InventorySummaryItem {
  lob: string;
  inv: number;
}

export interface RetailSummaryResponse {
  list: Array<RetailSummaryItem>;
  refresh_time?: string;
  updateTime?: string;
}

export interface RetailSummaryItem {
  name: string;
  cnt: number;
}
