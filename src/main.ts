import { createApp } from "vue";
import pinia from "./stores/pinia";
import "./style.css";

import App from "./App.vue";
import router from "./router";
import initLogListener from "@/utils/initLogListener";
// import { initIntermediatesManager } from "@/intermediates";
import "@dmp/components/dist/es/style.css";
import { i18n, loadLanguageAsync, getBrowserLanguage } from "./i18n";

const app = createApp(App);

app.use(pinia);
app.use(router);
app.use(i18n);
// 初始化日志监听
initLogListener(router, app);

// 初始化设置语音，如果用户第一次进入系统，则根据已经设置过的语言设置语言，如果用户没有设置过语言，则根据浏览器语言设置语言
const currentLanguage = sessionStorage.getItem("language") || getBrowserLanguage();
const currentLanguageSource = sessionStorage.getItem("languageSource") || "auto";

loadLanguageAsync(currentLanguage, currentLanguageSource).then(() => {
  app.mount("#app");
});
