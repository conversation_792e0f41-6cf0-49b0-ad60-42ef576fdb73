import { useMediaQuery } from "@vueuse/core";

export const useAppStore = defineStore("app", () => {
  // 状态
  const theme = ref("light");
  const currentUseLang = ref(null);
  const sidebar = ref({
    isOpen: true,
    isCollapsed: false,
    isExpanded: false,
    isMobileMenuOpen: false, // 移动端菜单是否打开
  });

  // 响应式媒体查询
  const isMobile = useMediaQuery("(max-width: 768px)");
  const isTablet = useMediaQuery("(max-width: 1024px)");

  // 计算属性
  const isDarkMode = computed(() => theme.value === "dark");

  // 计算侧边栏是否应该显示
  const shouldShowSidebar = computed(() => {
    if (isMobile.value) {
      return sidebar.value.isMobileMenuOpen;
    }
    return sidebar.value.isExpanded;
  });

  function toggleTheme() {
    theme.value = theme.value === "light" ? "dark" : "light";
  }

  function toggleSidebar() {
    sidebar.value.isOpen = !sidebar.value.isOpen;
  }

  function collapseSidebar() {
    sidebar.value.isCollapsed = !sidebar.value.isCollapsed;
  }

  function toggleExpanded() {
    if (isMobile.value) {
      // 移动端：切换移动端菜单状态
      sidebar.value.isMobileMenuOpen = !sidebar.value.isMobileMenuOpen;
    } else {
      // 桌面端：切换展开状态
      sidebar.value.isExpanded = !sidebar.value.isExpanded;
    }
  }

  // 关闭移动端菜单
  function closeMobileMenu() {
    sidebar.value.isMobileMenuOpen = false;
  }

  // 初始化响应式状态
  function initializeResponsiveState() {
    if (isMobile.value) {
      sidebar.value.isMobileMenuOpen = false;
    }
  }

  return {
    // state
    theme,
    currentUseLang,
    sidebar,
    // responsive state
    isMobile,
    isTablet,
    // computed
    isDarkMode,
    shouldShowSidebar,
    // actions
    toggleTheme,
    toggleSidebar,
    collapseSidebar,
    toggleExpanded,
    closeMobileMenu,
    initializeResponsiveState,
  };
});
