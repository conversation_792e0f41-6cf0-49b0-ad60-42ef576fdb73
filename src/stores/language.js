import { defineStore } from "pinia";
import { i18n } from "@/i18n";

export const useLanguage = defineStore("language", () => {
  const lang = ref(i18n.global.locale);
  const getLanguage = computed(() => {
    return (key) => {
      return i18n.global.messages[lang.value][key];
    };
  });

  watch(
    () => i18n.global.locale,
    (newLocale) => {
      lang.value = newLocale;
    },
    { immediate: true }
  );
  return {
    getLanguage,
  };
});
