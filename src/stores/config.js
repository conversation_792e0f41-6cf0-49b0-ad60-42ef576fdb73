import { getDisplayConfig, getFiscalWeeks, getUpdateTime, getFiscalDays } from "@/api/index.ts";

export const useConfigStore = defineStore("config", () => {
  const displayConfig = ref({});
  const fiscalWeeks = ref([]);
  const fiscalDays = ref([]);
  const updateTime = ref([]);
  const isConfigLoaded = ref(false);

  const arrayToTabs = (arr) => {
    if (!Array.isArray(arr)) return [];
    return arr.map((item) => ({ label: item, value: item }));
  };

  // 解析配置数据
  const parseConfigData = (data) => {
    try {
      // 解析config字段的JSON字符串
      // 过滤data.config
      const configData = JSON.parse(data.config);

      return {
        productRank: arrayToTabs(configData.product_ranking || []),
        salesWarning: arrayToTabs(configData.sales_waring || []),
        inventoryNotice: arrayToTabs(configData.inventory_notice || []),
        inventoryWarning: arrayToTabs(configData.inventory_waring || []),
        storeRank: arrayToTabs(configData.store_rank || []),
        salesPerformance: arrayToTabs(configData.sales_performance || []),
        inventoryPerformance: arrayToTabs(configData.inventory_performance || []),
        salesProject: arrayToTabs(configData.sales_project || []),
      };
    } catch (error) {
      console.error("解析配置数据失败:", error);
      return {};
    }
  };

  // 获取产品排行默认tabs
  const getProductRankDefaultTabs = computed(() => {
    return displayConfig.value.productRank || [];
  });

  // 获取销售预警tabs
  const getSalesWarningTabs = computed(() => {
    return displayConfig.value.salesWarning || [];
  });

  // 获取库存提醒tabs
  const getInventoryNoticeTabs = computed(() => {
    return displayConfig.value.inventoryNotice || [];
  });

  // 获取库存预警tabs
  const getInventoryWarningTabs = computed(() => {
    return displayConfig.value.inventoryWarning || [];
  });
  // 获取门店排行默认tabs
  const getStoreRankDefaultTabs = computed(() => {
    return displayConfig.value.storeRank || [];
  });

  // 获取销售表现默认tabs
  const getSalesPerformanceDefaultTabs = computed(() => {
    return displayConfig.value.salesPerformance || [];
  });

  // 获取库存表现默认tabs
  const getInventoryPerformanceDefaultTabs = computed(() => {
    return displayConfig.value.inventoryPerformance || [];
  });

  // 获取零售项目默认tabs
  const getSalesProjectDefaultTabs = computed(() => {
    return displayConfig.value.salesProject || [];
  });

  // 获取原始fiscal weeks
  const getFiscalWeeksOriginalData = computed(() => {
    return fiscalWeeks.value.sort((a, b) => a?.fiscal_week_year - b?.fiscal_week_year);
  });

  // 获取current week
  const getCurrentWeek = computed(() => {
    return {
      label:
        getFiscalWeeksOriginalData.value[getFiscalWeeksOriginalData.value.length - 1]
          ?.fiscal_qtr_week_name,
      value:
        getFiscalWeeksOriginalData.value[getFiscalWeeksOriginalData.value.length - 1]
          ?.fiscal_week_year,
    };
  });

  // 获取格式化后的fiscal weeks
  const getFormattedFiscalWeeks = computed(() => {
    return getFiscalWeeksOriginalData.value.map((item) => ({
      label: item.fiscal_qtr_week_name,
      value: item.fiscal_week_year,
    }));
  });

  // 根据moduleName获取更新时间
  const getUpdateTimeByModule = computed(() => {
    return updateTime.value.reduce((acc, curr) => {
      if (!curr) return acc;
      if (!curr.module_name) return acc;
      if (curr.module_name in acc) return acc;
      acc[curr.module_name] = curr;
      return acc;
    }, {});
  });

  // 获取原始fiscal days
  const getFiscalDaysOriginalData = computed(() => {
    return fiscalDays.value.sort((a, b) => a?.id - b?.id);
  });
  // 获取当前天
  const getCurrentDateAndWeek = computed(() => {
    return getFiscalDaysOriginalData.value?.[getFiscalDaysOriginalData.value.length - 1] ?? {};
  });

  // 获取格式化后的fiscal days
  const getFormattedFiscalDays = computed(() => {
    return getFiscalDaysOriginalData.value.map((item) => ({
      label: item?.fiscal_dt?.slice(5),
      value: item?.fiscal_dt,
    }));
  });

  // 获取current week
  const getCurrentDay = computed(() => {
    return {
      label:
        getFiscalDaysOriginalData.value[
          getFiscalDaysOriginalData.value.length - 1
        ]?.fiscal_dt?.slice(5),
      value: getFiscalDaysOriginalData.value[getFiscalDaysOriginalData.value.length - 1]?.fiscal_dt,
    };
  });

  // 初始化配置
  const initConfig = async (params) => {
    try {
      const [response, fiscalWeeksResponse, fiscalDaysResponse, updateTimeResponse] =
        await Promise.all([
          getDisplayConfig(params),
          getFiscalWeeks(),
          getFiscalDays(),
          getUpdateTime(),
        ]);

      if (response.code === 0 && response.data) {
        // 解析并转换配置数据
        const parsedConfig = parseConfigData(response.data);

        displayConfig.value = {
          ...displayConfig.value,
          ...parsedConfig,
        };
      } else {
        console.error("获取配置数据失败:", response);
      }

      if (fiscalWeeksResponse.code === 0 && fiscalWeeksResponse.data) {
        fiscalWeeks.value = fiscalWeeksResponse.data;
      } else {
        console.error("获取fiscal weeks失败:", fiscalWeeksResponse);
      }

      if (fiscalDaysResponse.code === 0 && fiscalDaysResponse.data) {
        fiscalDays.value = fiscalDaysResponse.data;
      } else {
        console.error("获取fiscal days失败:", fiscalDaysResponse);
      }

      if (updateTimeResponse.code === 0 && updateTimeResponse.data) {
        updateTime.value = updateTimeResponse.data;
      } else {
        console.error("获取update time失败:", updateTimeResponse);
      }
    } catch (error) {
      console.error("获取配置数据失败:", error);
    } finally {
      isConfigLoaded.value = true;
    }
  };

  return {
    // state
    displayConfig: readonly(displayConfig),
    fiscalWeeks: readonly(fiscalWeeks),
    updateTime: readonly(updateTime),
    fiscalDays: readonly(fiscalDays),
    isConfigLoaded: readonly(isConfigLoaded),

    // getters
    getStoreRankDefaultTabs,
    getProductRankDefaultTabs,
    getSalesWarningTabs,
    getInventoryNoticeTabs,
    getInventoryWarningTabs,
    getSalesPerformanceDefaultTabs,
    getInventoryPerformanceDefaultTabs,
    getSalesProjectDefaultTabs,
    getFiscalWeeksOriginalData,
    getCurrentWeek,
    getFormattedFiscalWeeks,
    getUpdateTimeByModule,
    getFiscalDaysOriginalData,
    getCurrentDay,
    getFormattedFiscalDays,
    getCurrentDateAndWeek,

    // actions
    initConfig,
  };
});
