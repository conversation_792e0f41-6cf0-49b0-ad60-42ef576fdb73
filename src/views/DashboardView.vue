<script setup lang="ts">
import { useUserStore } from "@/stores";
import { useAppStore } from "@/stores/app.js";
import ResellerLevelIcon from "@/assets/icons/reseller-level-icon.svg";
import DashboardHealth from "@/views/components/DashboardHealth/DashboardHealth.vue";
import DashboardOperationCenter from "@/views/components/DashboardOperationCenter/DashboardOperationCenter.vue";
import DashboardMessageCenter from "@/views/components/DashboardMessageCenter/DashboardMessageCenter.vue";
// import DashboardDataCenter from "@/views/components/DashboardDataCenter/DashboardCenter.vue";
import DashboardDataCenter from "@/views/components/DashboardDataCenter/DashboardDataCenter.vue";
import { useSchemaStore } from "@/stores/schema";
import { name2CompMap } from "@/constants/schema/name2CompMap";
import { useConfigStore } from "@/stores/config";
import DateIcon from "@/assets/icons/date-icon.svg";
import dayjs from "dayjs";

import "@/intermediates";

const userStore = useUserStore();
const appStore = useAppStore();
const configStore = useConfigStore();
const schemaStore = useSchemaStore();

// 获取响应式状态
const isMobile = computed(() => appStore.isMobile);
const currentDateAndWeek = computed(() => {
  const currentDate = dayjs(configStore.getCurrentDateAndWeek?.fiscal_dt).format("MM/DD");
  return `${configStore.getCurrentDateAndWeek?.fiscal_qtr_week_name ?? ""} ${currentDate ?? ""}`;
});

const showCurrentDateAndWeek = computed(() => {
  return Object.keys(configStore.getCurrentDateAndWeek).length > 0;
});

provide("isMobile", isMobile);

const resellerBadgeText = computed(() => {
  if (userStore.isRp) {
    return userStore.resellerType;
  }
  return "Carrier";
});
</script>

<template>
  <!-- <div>
    <el-select v-model="language" @change="changeLanguage" style="width: 240px">
      <el-option label="中文" value="zh" />
      <el-option label="英文" value="en" />
      <el-option label="繁体" value="hk" />
      <el-option label="简体" value="tw" />
    </el-select>
  </div> -->
  <dmp-popover-provider
    :schema="schemaStore.schema"
    :comps-registry="name2CompMap"
    :isMobile="isMobile"
    class="space-y-[24px]"
  >
    <div class="m-auto w-auto">
      <!-- Header -->
      <div
        :class="[
          'flex items-center gap-[16px]',
          // 移动端垂直布局，桌面端水平布局
          isMobile ? 'flex-col items-start' : 'justify-between',
        ]"
      >
        <div class="flex flex-col pl-[8px] w-full">
          <div class="min-h-[16px] mb-[8px]">
            <transition name="fade" mode="out-in">
              <div
                class="text-[13px] text-[rgba(0,0,0,0.72)] font-medium leading-[16px] flex items-center gap-[4px]"
                v-if="showCurrentDateAndWeek"
              >
                <DateIcon class="w-[16px] h-[16px]" />
                {{ currentDateAndWeek }}
              </div>
            </transition>
          </div>

          <div
            :class="['flex gap-[8px] items-center w-full', isMobile ? 'flex-col !items-start' : '']"
          >
            <h1
              :class="[
                'text-[24px] text-black font-medium truncate text-ellipsis overflow-hidden leading-[30px]',
                { 'w-full': isMobile },
              ]"
            >
              {{ userStore.resellerInfo?.reseller_name || "-" }}
            </h1>
            <div
              v-if="resellerBadgeText"
              class="flex items-center gap-[2px] bg-[rgba(255,255,255,.32)] rounded-[28px] px-[6px] py-[2px] shrink-0"
            >
              <ResellerLevelIcon class="w-[12px] h-[12px]" />
              <span class="text-[12px] text-[#000000]">
                {{ resellerBadgeText }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div
        :class="[
          'flex gap-[16px] mt-[24px]',
          // 移动端垂直布局，桌面端水平布局
          isMobile ? 'flex-col' : 'flex-row',
        ]"
      >
        <!-- 健康诊断组件 -->
        <div
          :class="[
            'flex flex-col gap-[16px]',
            // 移动端全宽，桌面端固定宽度
            isMobile ? 'w-full' : 'w-[332px] shrink-0',
          ]"
        >
          <DashboardHealth
            class="w-full flex-1"
            v-log.visible="{ event_name: 'dashboard_health' }"
            v-log.stay="{ event_name: 'dashboard_health' }"
          />
        </div>

        <!-- 消息中心和运营中心 -->
        <div
          class="flex flex-col gap-[16px] overflow-hidden shrink-0"
          :class="[isMobile ? 'w-full' : 'w-[720px]']"
        >
          <DashboardMessageCenter
            :class="[
              'w-full flex flex-col',
              // 移动端调整高度，桌面端保持原高度
              isMobile ? 'h-auto min-h-[120px]' : 'h-[144px]',
            ]"
          />

          <!-- 运营中心 -->
          <DashboardOperationCenter :class="['w-full', isMobile ? 'h-auto' : 'h-[384px]']" />
        </div>
      </div>
      <!-- 数据中心 -->
      <DashboardDataCenter
        :class="[
          'mt-[16px]',
          isMobile ? 'w-full' : 'w-[1068px]',
          // 移动端调整高度，桌面端保持原高度
          isMobile ? 'h-auto min-h-[300px]' : 'h-auto max-h-[384px]',
        ]"
      />
    </div>
  </dmp-popover-provider>
</template>

<style scoped>
/* 确保卡片在小屏幕上的响应式行为 */
@media (max-width: 1024px) {
  .col-span-12 {
    grid-column: span 12;
  }
}

/* 卡片悬停效果 */
.bg-white:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.dashboard-score-wrapper {
  background: url(../assets/dashboard/dashboard-score-bg.png) rgba(255, 255, 255, 0.32);
  background-size: cover;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
