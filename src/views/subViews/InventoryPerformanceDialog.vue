<!--
 * @Date: 2025-07-16 18:44:17
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-31 15:14:15
 * @FilePath: /MyBusinessV2/src/views/subViews/InventoryPerformanceDialog.vue
-->
<template>
  <!-- 库存表现 -->
  <BasicDialog>
    <template #header>
      <DialogHeader :title="transferLangByKey" @download-click="handleDownload" />
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="cycles"
        :current-week="currentWeek.value"
        @change="handleCycleChange"
      />
    </template>
    <template #default>
      <dmp-detail-table
        :title="$t('库存详情')"
        :columns="columns"
        :data="tableData"
        class="my-[12px]"
        :unitMap="unitMap"
        :loading="loading"
        :height-class="'!h-[180px]'"
        :UnitPresetMode="['inv', 'woi']"
      />
      <dmp-bar-stack-chart
        title="库存分布"
        :subtitle="subtitle"
        :xAxisData="xAxisData"
        :series="series"
        :legendNames="legendNames"
        :loading="loading"
        :first-render="firstRender"
      />
    </template>
  </BasicDialog>
</template>

<script setup lang="ts">
import {
  getInventoryDialogInfo,
  getInventoryEchartDialogInfo,
  getInventoryPerformanceDownload,
} from "@/api/index";
import { downloadFile } from "@/utils/downloadFile";
import { DialogHeader } from "./components/DialogHeader";
import BasicDialog from "./components/BasicDialog.vue";
import { useI18n } from "vue-i18n";
import { useCycleSelector } from "./components/useCycleSelector";
import { useUserStore } from "@/stores";

const { t } = useI18n();
const props = defineProps<{ lob?: string; moduleName?: string }>();
const lob = props.lob ?? "iPhone";
const userStore = useUserStore();
const { cycles, current, currentWeek } = useCycleSelector(props.moduleName, false);

// 类型定义
// interface Metric {
//   name: string;
//   value: number | string;
//   value_type: string;
//   unit?: string;
// }
interface InventoryDetail {
  fiscal_qtr_week_name: string;
  inv_data: Record<string, any>[];
}
interface Column {
  label: string;
  prop: string;
}

const subtitle = computed(() => {
  return t("每日门店库存状态");
});

const currentCycle = ref<string | number | null>(current.value?.value ?? null);
const tableData = ref<Record<string, any>[]>([]);
const echartData = ref<Record<string, any>[]>([]);
const legendNames = ref<string[]>([]);
const loading = ref(true);
const firstRender = ref(true);

const isRp = computed(() => userStore.isRp);
const columns = computed<Column[]>(() => {
  const baseColumns = [{ label: t("产品型号"), prop: "sub_lob" }];

  if (isRp.value) {
    return [
      ...baseColumns,
      { label: t("门店库存"), prop: "inv", width: 88 },
      {
        label: t("库存周转"),
        prop: "woi",
        width: 100,
        formatter: (row: { woi: string | number | null }) => {
          if (row.woi != null && row.woi !== "" && row.woi !== "-") {
            return Number(row.woi).toFixed(1);
          }
          return "-";
        },
      },
    ];
  } else {
    return [
      ...baseColumns,
      { label: t("门店库存"), prop: "inv", width: 88 },
      {
        label: t("门店库存周转"),
        prop: "woi",
        width: 100,
        formatter: (row: { woi: string | number | null }) => {
          if (row.woi != null && row.woi !== "" && row.woi !== "-") {
            return Number(row.woi).toFixed(1);
          }
          return "-";
        },
      },
    ];
  }
});

const xAxisData = ref<string[]>([]); // x轴数据
const series = ref<{ data: number[]; color: string; name: string }[]>([]); // y轴数据
const state = ref<InventoryDetail[]>([]);
const unitMap: Record<string, string> = {
  inv: "台",
  woi: "周",
};
const wordKey = ref("库存表现");

const dialogHeaderTitle = computed(() => {
  return lob ? `${lob} ${wordKey.value}` : wordKey.value;
});
const transferLangByKey = computed(() => {
  return dialogHeaderTitle.value.replaceAll(wordKey.value, t(wordKey.value));
});

const handleDownload = async () => {
  try {
    const res = await getInventoryPerformanceDownload({
      lob,
      fiscal_dt: currentCycle.value,
    });
    try {
      downloadFile(res);
    } catch (error) {
      ElMessage.error("下载失败：" + (error as Error).message);
    }
  } catch (error) {
    ElMessage.error("请求失败：" + (error as Error).message);
  }
};

const handleCycleChange = () => {
  if (!currentCycle.value) return;
  firstRender.value = false;
  const found = state.value.find((item) => item.fiscal_qtr_week_name === currentCycle.value);
  if (found) {
    tableData.value = found.inv_data;
  } else {
    tableData.value = [];
  }

  const currentEchartData = echartData.value.find(
    (item: any) => item.fiscal_qtr_week_name === currentCycle.value
  );
  if (currentEchartData) {
    xAxisData.value = currentEchartData.datas.map((item: any) => item.sub_lob);
    const baseSeries = [
      {
        name: t("门店"),
        data: currentEchartData.datas.map((item: any) => item.pos_inv),
        color: isRp.value ? "#59ADC4" : "#BD74E4",
      },
    ];
    if (!isRp.value) {
      subtitle.value = t("每日子产品库存状态");
      legendNames.value = [t("在仓"), t("门店")];
      baseSeries.unshift({
        name: t("在仓"),
        data: currentEchartData.datas.map((item: any) => item.warehouse_inv),
        color: "#5F6CC0",
      });
    }
    series.value = baseSeries;
  } else {
    xAxisData.value = [];
    series.value = [];
  }
};

onMounted(() => {
  Promise.all([
    getInventoryEchartDialogInfo({ lob, fiscal_dt: currentCycle.value }),
    getInventoryDialogInfo({ lob, fiscal_dt: currentCycle.value }),
  ])
    .then(([echartRes, baseRes]) => {
      echartData.value = echartRes.data;
      state.value = baseRes.data;
      handleCycleChange();
    })
    .catch((error) => {
      console.error("请求失败:", error);
    })
    .finally(() => {
      loading.value = false;
    });
});
</script>
