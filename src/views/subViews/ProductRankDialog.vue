<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getProductRankDialogInfo } from "@/api/index";
import { formatProductRank } from "@/intermediates/format/formatProductRankDialog";
import type { PopoverRankItem } from "@dmp/components";
import { DialogHeader } from "./components/DialogHeader";
import BasicDialog from "./components/BasicDialog.vue";
import { useCycleSelector } from "./components/useCycleSelector";
import { useUserStore } from "@/stores";

const { lob, moduleName } = defineProps<{ lob: string; moduleName: string }>();
const { t } = useI18n();
const { current } = useCycleSelector(moduleName);
const userStore = useUserStore();

// tab
const tabOptions = computed(() => {
  const array = [
    { label: t("市场"), value: "market" },
    { label: t("渠道"), value: "channel" },
    { label: userStore.isRp ? t("经销商") : t("运营商"), value: "reseller" },
  ];
  if (!userStore.isRp) return array.filter((item) => item.value !== "channel");
  return array;
});
const tabValue = ref(tabOptions.value[0].value);

// selector
const selectOptions = computed(() => [
  { label: t("上周"), value: "last_week" },
  { label: t("季度累计"), value: "current_quarter" },
]);
const selectValue = ref(selectOptions.value[0].value);

// api
const rankData: Ref<Record<string, PopoverRankItem[]>> = ref({});
const loading = ref(false);
watchEffect(() => {
  loading.value = true;
  getProductRankDialogInfo({
    lob,
    start: 0,
    offset: 10,
    fiscal_week_year: current.value?.value ?? null,
  })
    .then(({ data = {} }) => {
      rankData.value = formatProductRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});

// computed
const currentRankData = computed(() => {
  const key = `${selectValue.value}_${tabValue.value}`;
  return rankData.value?.[key] ?? [];
});

const wordKey = ref("热销排行");

const dialogHeaderTitle = computed(() => {
  return lob ? `${lob} ${wordKey.value}` : wordKey.value;
});
const transferLangByKey = computed(() => {
  return dialogHeaderTitle.value.replaceAll(wordKey.value, t(wordKey.value));
});

// methods
const setValue = (value: string | number) => {
  return value ? `${value}%` : "-";
};
</script>

<template>
  <BasicDialog :show-line="false" :body-class="'flex flex-col gap-[12px] mt-[12px]'">
    <template #header>
      <DialogHeader :title="transferLangByKey" :showDownload="false" />
      <div class="w-full h-[1px] bg-[rgba(28,28,30,0.08)] my-[4px]" />
      <DmpTabSwitcher :tabs="tabOptions" v-model:defaultTab="tabValue" equalWidth />
    </template>
    <template #default>
      <DmpPopoverRank
        class="flex flex-col flex-1"
        :value-title="t('占有率')"
        :columns="['1fr', '80px']"
        :list="currentRankData"
        :loading="loading"
      >
        <template v-slot:nameTitle>
          <DmpSimpleDropdown v-model="selectValue" :options="selectOptions" />
        </template>
        <template v-slot:value="{ row }">
          <span class="text-[16px] leading-[20px] text-[#3948B1] font-semibold">{{
            setValue(row?.value)
          }}</span>
        </template>
      </DmpPopoverRank>
    </template>
  </BasicDialog>
</template>
