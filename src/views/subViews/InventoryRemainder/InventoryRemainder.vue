<script setup lang="ts">
import { useInventoryRemainder } from "./hooks/useInventoryRemainder";
import { DialogHeader } from "../components/DialogHeader";
import BasicDialog from "../components/BasicDialog.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const {
  currentCycle,
  currentWeek,
  tabs,
  hideTabs,
  initData,
  dates,
  tableData,
  currentLob,
  columns,
  handleDownload,
  unitMap,
  loading,
  chartsXAxisData,
  chartsSeries,
  chartsLoading,
} = useInventoryRemainder("healthDiagnosis");

onMounted(() => {
  initData();
});

const firstRender = ref(true);
const handleCycleChange = () => {
  firstRender.value = false;
};

const handleTabChange = (val: string) => {
  currentLob.value = val;
};
const title = computed(() => {
  return tabs.value.length === 1 ? `${tabs.value[0].label} ${t("库存预警")}` : t("库存预警");
});
</script>
<template>
  <BasicDialog>
    <template #header>
      <DialogHeader :title="title" @download-click="handleDownload" />
      <dmp-text-list
        :texts="[
          t('各子产品线因连续无库存而触发的库存预警次数。'),
          t('根据连续无库存天数长短，分为“非常严重”，“严重”，“中等”。'),
        ]"
      />
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="dates"
        :current-week="currentWeek"
        @update:modelValue="(val) => $emit('update:currentCycle', val)"
        @change="handleCycleChange"
      />
    </template>
    <template #default>
      <div class="flex flex-col gap-[16px] mt-[12px]">
        <dmp-tab-switcher
          v-if="!hideTabs"
          @change="handleTabChange"
          :tabs="tabs"
          :equal-width="true"
        />

        <div class="flex flex-col gap-[8px]">
          <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">
            {{ t("预警分布") }}
          </h1>
          <dmp-detail-table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :unit-map="unitMap"
            :UnitPresetMode="['warning_times', 'warning_store']"
            :height-class="'!h-[122px]'"
          />
        </div>

        <div class="flex flex-col gap-[8px]">
          <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">
            {{ t("预警趋势") }}
          </h1>
          <dmp-bar-stack-chart
            subtitle="每周预警次数"
            :xAxisData="chartsXAxisData"
            :series="chartsSeries"
            :legendNames="['非常严重', '严重', '中等']"
            :loading="chartsLoading"
            :height-class="'!h-[174px]'"
            :first-render="firstRender"
          />
        </div>
      </div>
    </template>
  </BasicDialog>
</template>
