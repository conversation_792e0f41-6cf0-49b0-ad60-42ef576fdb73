import {
  getInventoryWarningInfo,
  getInventoryWarningDownload,
  getInventoryWarningTrend,
} from "@/api";
import { useConfigStore } from "@/stores/config";
import {
  InventoryWarningDialogResponse,
  InventoryWarningTrendResponse,
} from "@/api/interface/InventoryWarningDialog";
import { downloadFile } from "@/utils/downloadFile";
import { useI18n } from "vue-i18n";
import { useCycleSelector, type CycleItem } from "../../components/useCycleSelector";
import { fiscal2Number } from "@/utils/util";

const configStore = useConfigStore();

export function useInventoryRemainder(moduleName: string) {
  const { cycles, current, currentWeek } = useCycleSelector(moduleName, false);
  const { current: currentWeekYear } = useCycleSelector(moduleName);
  const { t } = useI18n();
  const currentCycle = ref(current.value?.value ?? "");
  const dates = ref<CycleItem[]>(cycles.value);
  const resData = ref<InventoryWarningDialogResponse[]>([]);
  const currentLob = ref<string>("");
  const loading = ref(false);
  const chartsLoading = ref(false);
  const chartsXAxisData = ref<string[]>([]);
  const chartsSeries = ref<{ name: string; data: number[] }[]>([]);

  const unitMap: Record<string, string> = {
    warning_times: "次",
    warning_store: "家",
  };

  // 根据选中的日期获取对应的数据
  const currentDateData = computed(() => {
    if (!resData.value.length || !currentCycle.value) return null;
    return resData.value.find((item) => item.date === currentCycle.value);
  });

  // 动态生成tabs数据
  const tabs = computed(() => {
    // 确保配置加载完成后再获取tabs
    if (!configStore.isConfigLoaded) {
      return [];
    }
    return configStore.getInventoryWarningTabs;
  });

  const hideTabs = computed(() => {
    return tabs.value.length <= 1;
  });

  // 工具函数：转换level名称为中文显示
  const getLevelDisplayName = (level: string) => {
    const levelMap = {
      very_severe: t("非常严重"),
      severe: t("严重"),
      medium: t("中等"),
    };
    return levelMap[level] || level;
  };

  // 根据选中的LOB获取对应的levels数据
  const tableData = computed(() => {
    if (!currentDateData.value?.warning_lobs?.length || !currentLob.value) return [];

    const selectedLobData = currentDateData.value.warning_lobs.find(
      (item) => item.lob === currentLob.value
    );

    if (!selectedLobData?.levels) return [];

    return selectedLobData.levels.map((level) => ({
      level: getLevelDisplayName(level.level),
      warning_times: level.warn_cnt?.toString() || "0",
      warning_store: level.pos_cnt?.toString() || "0",
    }));
  });

  // 表格列定义
  const columns = computed(() => [
    {
      label: "",
      prop: "level",
      width: 136,
    },
    {
      label: t("预警次数"),
      prop: "warning_times",
      width: 96,
    },
    {
      label: t("预警门店"),
      prop: "warning_store",
      width: 88,
    },
  ]);

  const loadInventoryWarningInfo = async () => {
    loading.value = true;
    try {
      const res = await getInventoryWarningInfo({
        fiscal_dt: currentCycle.value,
      });

      if (res.data && Array.isArray(res.data)) {
        // 过滤有效数据
        const validData = res.data.filter((item: InventoryWarningDialogResponse) => item?.date);
        resData.value = validData;
      } else {
        console.log("🔍 数据为空");
      }
    } catch (error) {
      console.error("获取库存余量数据失败:", error);
      // 容错处理：重置数据
      dates.value = [];
      resData.value = [];
      currentCycle.value = null;
      currentLob.value = "";
    } finally {
      loading.value = false;
    }
  };

  const loadInventoryWarningTrend = async () => {
    chartsLoading.value = true;
    try {
      const res = await getInventoryWarningTrend({
        fiscal_week_year: currentWeekYear.value?.value,
      });

      if (res.data && Array.isArray(res.data)) {
        const sortedData = res.data.sort((a, b) => {
          return fiscal2Number(a.fiscal_qtr_week_name) - fiscal2Number(b.fiscal_qtr_week_name);
        });
        // 提取 week_name 作为 x 轴数据
        chartsXAxisData.value = sortedData.map(
          (item: InventoryWarningTrendResponse) => item.week_name
        );
        const veryServerData = sortedData.map(
          (item: InventoryWarningTrendResponse) => item.very_severe || 0
        );
        const severeData = sortedData.map(
          (item: InventoryWarningTrendResponse) => item.severe || 0
        );
        const mediumData = sortedData.map(
          (item: InventoryWarningTrendResponse) => item.medium || 0
        );

        chartsSeries.value = [
          { name: "非常严重", data: veryServerData },
          { name: "严重", data: severeData },
          { name: "中等", data: mediumData },
        ];
      }
    } catch (error) {
      console.error("获取库存预警趋势数据失败:", error);
    } finally {
      chartsLoading.value = false;
    }
  };

  // 监听日期变化和配置加载，设置第一个可用的LOB
  watch(
    [currentCycle, tabs, () => configStore.isConfigLoaded],
    () => {
      // 当日期变化或tabs变化或配置加载完成时，自动选择第一个可用的LOB
      if (tabs.value.length > 0 && !currentLob.value) {
        currentLob.value = tabs.value[0].value as string;
      } else if (tabs.value.length === 0 && configStore.isConfigLoaded) {
        // 配置已加载但tabs为空，说明没有配置或配置为空
        currentLob.value = "";
      }
    },
    { immediate: true }
  );

  const initData = () => {
    loadInventoryWarningInfo();
    loadInventoryWarningTrend();
  };

  const handleDownload = async () => {
    try {
      const res = await getInventoryWarningDownload({
        date: currentCycle.value,
      });
      downloadFile(res);
    } catch (error) {
      console.error("下载文件失败:", error);
    }
  };

  return {
    currentCycle,
    currentWeek: currentWeek.value.value,
    tabs,
    hideTabs,
    initData,
    dates,
    tableData,
    currentLob,
    columns,
    unitMap,
    handleDownload,
    loading,
    chartsLoading,
    chartsXAxisData,
    chartsSeries,
  };
}
