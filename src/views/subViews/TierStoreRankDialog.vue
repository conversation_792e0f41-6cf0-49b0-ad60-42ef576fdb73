<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getStoreRankDialogInfo, getStoreRankDownload } from "@/api/index";
import { formatTierStoreRank } from "@/intermediates/format/formatStoreRankDialog";
import type { PopoverRankItem } from "@dmp/components";
import { DialogHeader } from "./components/DialogHeader";
import BasicDialog from "./components/BasicDialog.vue";
import { useCycleSelector } from "./components/useCycleSelector";
import { downloadFile } from "@/utils/downloadFile";
const { lob, moduleName, lobzh } = defineProps<{
  lob: string;
  moduleName: string;
  lobzh: string;
}>();
const { t } = useI18n();
const { cycles, current, currentWeek } = useCycleSelector(moduleName);

// tab
const tabOptions = ref([
  { label: "Tier 1", value: "Tier 1" },
  { label: "Tier 2", value: "Tier 2" },
  { label: "Tier 3", value: "Tier 3" },
]);
const tabValue = ref(tabOptions.value[0].value);
const wordKey = ref("门店排名");

const dialogHeaderTitle = computed(() => {
  return lobzh ? `${t(lobzh)} ${t(wordKey.value)}` : t(wordKey.value);
});
const transferLangByKey = computed(() => {
  return dialogHeaderTitle.value.replaceAll(wordKey.value, t(wordKey.value));
});

// cycle selector
const currentCycle = ref(current.value?.value ?? null);

// api
const rankData: Ref<PopoverRankItem[]> = ref([]);
const loading = ref(false);
watchEffect(() => {
  loading.value = true;
  getStoreRankDialogInfo({ metric: lob, tab: tabValue.value, fiscal_week_year: currentCycle.value })
    .then(({ data = [] }) => {
      rankData.value = formatTierStoreRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});
const handleDownload = async () => {
  try {
    const res = await getStoreRankDownload({
      fiscal_week_year: currentCycle.value,
    });
    try {
      downloadFile(res);
    } catch (error) {
      ElMessage.error("下载失败：" + (error as Error).message);
    }
  } catch (error) {
    ElMessage.error("请求失败：" + (error as Error).message);
  }
};
</script>

<template>
  <BasicDialog :show-line="false" :body-class="'flex flex-col gap-[12px] mt-[12px]'">
    <template #header>
      <DialogHeader :title="transferLangByKey" @download-click="handleDownload" />
      <DmpCycleSelector v-model="currentCycle" :cycles="cycles" :current-week="currentWeek.value" />
      <div class="w-full h-[1px] bg-[rgba(28,28,30,0.08)] my-[4px]" />
      <DmpTabSwitcher :tabs="tabOptions" v-model:defaultTab="tabValue" equalWidth />
    </template>
    <template #default>
      <DmpPopoverRank
        class="flex flex-col flex-1"
        :name-title="t('排名')"
        :columns="['1fr']"
        :list="rankData"
        :loading="loading"
      />
    </template>
  </BasicDialog>
</template>
