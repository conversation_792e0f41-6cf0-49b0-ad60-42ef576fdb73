<template>
  <BasicDialog>
    <template #header>
      <DialogHeader
        :title="dialogHeaderTitle"
        downloadText="下载明细"
        @download-click="handleDownload"
      />
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="cycles"
        :current-week="currentWeek.value"
        @change="handleCycleChange"
      />
    </template>
    <template #default>
      <dmp-info-detail-table
        class="my-[16px]"
        :items="salesDetails"
        :title="t('销售详情')"
        :loading="loading"
        :height-class="'!h-[202px]'"
      />
      <dmp-line-chart
        title="销售趋势"
        subtitle="每周销量汇总"
        :xAxisData="xAxisData"
        :series="series"
        :loading="loading"
        :first-render="firstRender"
      />
    </template>
  </BasicDialog>
</template>

<script setup lang="ts">
import {
  getSalesPerformanceDialogInfo,
  getSalesEchartDialogInfo,
  getSalesPerformanceDownload,
} from "@/api/index";
import { downloadFile } from "@/utils/downloadFile";
import { DialogHeader } from "./components/DialogHeader";
import { useI18n } from "vue-i18n";
import { formatNumber } from "@/utils/number";
import BasicDialog from "./components/BasicDialog.vue";
import { SalesEchartDialogItem } from "@/api/interface/SalesPerformanceDialog";
import { useCycleSelector } from "./components/useCycleSelector";
import { fiscal2Number, calcRollingList } from "@/utils/util";

interface Metric {
  name: string;
  value: number | string;
  value_type: string;
  unit?: string;
}
interface SalesDetail {
  fiscal_qtr_week_name: string;
  metrics: Metric[];
}
const { t } = useI18n();
const { lob, moduleName } = defineProps<{ lob?: string; moduleName?: string }>();
const { cycles, current, currentWeek } = useCycleSelector(moduleName);

const currentCycle: Ref<string | number | null> = ref(current.value?.value ?? null);
const salesDetails: Ref<Metric[]> = ref([]);
const xAxisData: Ref<string[]> = ref([]); // x轴数据
const series: Ref<{ data: number[] }[]> = ref([]); // y轴数据
const state: Ref<SalesDetail[]> = ref([]);
const chartData: Ref<SalesEchartDialogItem[]> = ref([]);
const loading = ref(true);
const firstRender = ref(true);
const dialogHeaderTitle = computed(() => {
  return lob ? `${lob} ${t("销售表现")}` : t("销售表现");
});

const currentLabel = computed(() => {
  return cycles.value.find((item) => item.value === currentCycle.value)?.label;
});

const handleDownload = async () => {
  try {
    const res = await getSalesPerformanceDownload({
      fiscal_week_year: currentCycle.value,
    });
    try {
      downloadFile(res);
    } catch (error) {
      ElMessage.error("下载失败：" + (error as Error).message);
    }
  } catch (error) {
    ElMessage.error("请求失败：" + (error as Error).message);
  }
};

const setSalesDetails = () => {
  if (!currentLabel.value) return;
  const found = state.value.find((item) => item.fiscal_qtr_week_name === currentLabel.value);
  if (found) {
    salesDetails.value = found.metrics;
    salesDetails.value.map((item) => {
      if (item.value_type === "number") {
        item.unit = "台";
        item.value = formatNumber(item.value);
      } else if (item.value != null && typeof item.value === "number") {
        item.unit = "%";
      }
    });
  } else {
    salesDetails.value = [];
    xAxisData.value = [];
    series.value = [];
  }

  const currentEchartData = calcRollingList(
    chartData.value,
    "fiscal_qtr_week",
    currentLabel.value,
    13
  );
  xAxisData.value = currentEchartData.map((item: SalesEchartDialogItem) => item.fiscal_week);
  series.value = [{ data: currentEchartData.map((item: SalesEchartDialogItem) => item.so) }];
};

onMounted(() => {
  if (lob) {
    Promise.all([
      getSalesEchartDialogInfo({ lob, fiscal_week_year: currentCycle.value }),
      getSalesPerformanceDialogInfo({ lob, fiscal_week_year: currentCycle.value }),
    ])
      .then(([echartRes, baseRes]) => {
        chartData.value = echartRes.data.sort((a, b) => {
          return fiscal2Number(a.fiscal_qtr_week) - fiscal2Number(b.fiscal_qtr_week);
        });
        state.value = baseRes.data;
        setSalesDetails();
      })
      .catch((error) => {
        console.error("请求失败:", error);
      })
      .finally(() => {
        loading.value = false;
      });
  }
});

const handleCycleChange = () => {
  firstRender.value = false;
  setSalesDetails();
};
</script>
