import { getInventoryRemainderInfo, getInventoryRemainderDownload } from "@/api";
import { useConfigStore } from "@/stores/config";
import { InventoryRemindResponse } from "@/api/interface/InventoryRemind";
import { downloadFile } from "@/utils/downloadFile";
import { useCycleSelector, type CycleItem } from "../../components/useCycleSelector";

const configStore = useConfigStore();

export function useInventoryWarningDialog(moduleName: string) {
  const { cycles, current, currentWeek } = useCycleSelector(moduleName, false);

  const dates = ref<CycleItem[]>(cycles.value);
  const currentCycle = ref<string | number>(current.value?.value ?? "");
  const resData = ref<InventoryRemindResponse[]>([]);
  const currentLob = ref<string>("");

  // 根据选中的日期获取对应的数据
  const currentDateData = computed(() => {
    if (!resData.value.length || !currentCycle.value) return null;
    return resData.value.find((item) => item.date === currentCycle.value);
  });

  // 动态生成tabs数据
  const tabs = computed(() => {
    // 确保配置加载完成后再获取tabs
    if (!configStore.isConfigLoaded) {
      return [];
    }
    return configStore.getInventoryNoticeTabs;
  });

  const hideTabs = computed(() => {
    return tabs.value.length <= 1;
  });

  // 计算当前选中LOB的库存统计数据
  const currentLobStats = computed(() => {
    if (!currentDateData.value?.inv_remind_lobs?.length || !currentLob.value) {
      return {
        woi_less_1: { inventory: 0, ratio: 0 },
        woi_greater_5: { inventory: 0, ratio: 0 },
        total_inventory: 0,
      };
    }

    const selectedLobData = currentDateData.value.inv_remind_lobs.find(
      (item) => item.lob === currentLob.value
    );

    if (!selectedLobData) {
      return {
        woi_less_1: { inventory: 0, ratio: 0 },
        woi_greater_5: { inventory: 0, ratio: 0 },
        total_inventory: 0,
      };
    }

    const woi_less_1_inv = selectedLobData.woi_less_1_inv || 0;
    const woi_greater_5_inv = selectedLobData.woi_greater_5_inv || 0;
    const total_inventory = selectedLobData.inv || 0;

    return {
      woi_less_1: {
        inventory: woi_less_1_inv,
        ratio: (selectedLobData.woi_less_1_inv_ratio || 0) * 100,
      },
      woi_greater_5: {
        inventory: woi_greater_5_inv,
        ratio: (selectedLobData.woi_greater_5_inv_ratio || 0) * 100,
      },
      total_inventory,
    };
  });

  const loadInventoryRemainderInfo = async () => {
    try {
      const res = await getInventoryRemainderInfo({
        fiscal_dt: currentCycle.value,
      });

      if (res.data && Array.isArray(res.data)) {
        // 过滤有效数据
        const validData = res.data.filter((item: InventoryRemindResponse) => item?.date);
        resData.value = validData;
      }
    } catch (error) {
      console.error("加载库存提醒信息失败:", error);
      // 容错处理：重置数据
      dates.value = [];
      resData.value = [];
      currentCycle.value = null;
      currentLob.value = "";
    }
  };

  const initData = () => {
    loadInventoryRemainderInfo();
  };

  const handleDownload = async () => {
    try {
      const res = await getInventoryRemainderDownload({
        date: currentCycle.value,
      });
      downloadFile(res);
    } catch (error) {
      console.error("下载文件失败:", error);
    }
  };

  // 监听日期变化和配置加载，设置第一个可用的LOB
  watch(
    [currentCycle, tabs, () => configStore.isConfigLoaded],
    () => {
      // 当日期变化或tabs变化或配置加载完成时，自动选择第一个可用的LOB
      if (tabs.value.length > 0 && !currentLob.value) {
        currentLob.value = tabs.value[0].value as string;
      } else if (tabs.value.length === 0 && configStore.isConfigLoaded) {
        // 配置已加载但tabs为空，说明没有配置或配置为空
        currentLob.value = "";
      }
    },
    { immediate: true }
  );

  return {
    currentCycle,
    currentWeek: currentWeek.value.value,
    tabs,
    hideTabs,
    initData,
    dates,
    currentLob,
    currentLobStats,
    handleDownload,
  };
}
