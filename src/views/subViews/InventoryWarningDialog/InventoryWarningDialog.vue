<script setup lang="ts">
import { useInventoryWarningDialog } from "./hooks/useInventoryWarningDialog";
import { formatNumber } from "../../../../packages/common/utils/number";
import { DialogHeader } from "../components/DialogHeader";
import BasicDialog from "../components/BasicDialog.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const {
  currentCycle,
  currentWeek,
  tabs,
  hideTabs,
  initData,
  dates,
  currentLob,
  currentLobStats,
  handleDownload,
} = useInventoryWarningDialog("healthDiagnosis");

onMounted(() => {
  initData();
});

const handleTabChange = (val: string) => {
  currentLob.value = val;
};
const texts = computed(() => {
  return [t("基于过往 5 周平均销量得出的结果。")];
});
const title = computed(() => {
  return tabs.value.length === 1 ? `${tabs.value[0].label} ${t("库存提醒")}` : t("库存提醒");
});
</script>
<template>
  <BasicDialog>
    <template #header>
      <DialogHeader :title="title" @download-click="handleDownload" />
      <dmp-text-list :texts="texts" />
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="dates"
        :current-week="currentWeek"
        @update:modelValue="(val) => $emit('update:currentCycle', val)"
      />
    </template>
    <template #default>
      <div class="flex flex-col gap-[16px] mt-[12px]">
        <dmp-tab-switcher
          v-if="!hideTabs"
          @change="handleTabChange"
          :tabs="tabs"
          :equal-width="true"
        />

        <div class="flex flex-col gap-[8px]">
          <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">
            {{ $t("缺货分布") }}
          </h1>

          <div class="flex flex-col gap-[4px]">
            <div class="bg-[rgba(28,28,30,0.06)] rounded-[12px] py-[4px] px-[12px]">
              <div class="flex justify-between h-[48px] items-center">
                <div>
                  <div class="text-[13px] font-normal leading-[16px] text-[#1C1C1E]">
                    {{ $t("大于5周") }}
                  </div>
                  <div class="text-[12px] font-normal leading-[16px] text-[#AEAEB2]">
                    {{ $t("库存") }} {{ formatNumber(currentLobStats.woi_greater_5.inventory) }}
                  </div>
                </div>
                <dmp-value-with-unit
                  :value="currentLobStats.woi_greater_5.ratio.toFixed(1)"
                  unit="%"
                />
              </div>
              <div class="h-[1px] bg-[rgba(28,28,30,0.08)]"></div>
              <div class="flex justify-between h-[48px] items-center">
                <div>
                  <div class="text-[13px] font-normal leading-[16px] text-[#1C1C1E]">
                    {{ $t("小于1周") }}
                  </div>
                  <div class="text-[12px] font-normal leading-[16px] text-[#AEAEB2]">
                    {{ $t("库存") }} {{ formatNumber(currentLobStats.woi_less_1.inventory) }}
                  </div>
                </div>
                <dmp-value-with-unit
                  :value="currentLobStats.woi_less_1.ratio.toFixed(1)"
                  unit="%"
                />
              </div>
            </div>

            <div class="bg-[rgba(28,28,30,0.06)] rounded-[12px] px-[12px] py-[4px]">
              <div class="flex justify-between h-[40px] items-center">
                <div>
                  <div class="text-[13px] font-normal leading-[16px] text-[#1C1C1E]">
                    {{ $t("累计库存") }}
                  </div>
                </div>
                <dmp-value-with-unit
                  :value="formatNumber(currentLobStats.total_inventory)"
                  :unit="$t('台')"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </BasicDialog>
</template>
