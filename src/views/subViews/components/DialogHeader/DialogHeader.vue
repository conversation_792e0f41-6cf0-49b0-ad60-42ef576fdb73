<script setup lang="ts">
import { useAppStore } from "@/stores/app.js";

interface DownloadHeaderProps {
  /** 标题文本，当没有使用 title slot 时显示 */
  title?: string;
  /** 下载按钮文本 */
  downloadText?: string;
  /** 下载按钮图标名称 */
  downloadIcon?: string;
  /** 是否显示下载按钮 */
  showDownload?: boolean;
  /** 下载按钮自定义样式类 */
  downloadClass?: string;
  /** 标题自定义样式类 */
  titleClass?: string;
}

withDefaults(defineProps<DownloadHeaderProps>(), {
  title: "",
  downloadText: "下载明细",
  downloadIcon: "download",
  showDownload: true,
  downloadClass: "",
  titleClass: "",
});

const emit = defineEmits<{
  "download-click": [event: MouseEvent];
}>();

const { isMobile } = storeToRefs(useAppStore());

function handleDownloadClick(event: MouseEvent) {
  emit("download-click", event);
}
</script>

<template>
  <div class="flex justify-between items-center" :class="{ 'pr-[36px]': isMobile }">
    <!-- 标题区域 直接传入翻译后的title -->
    <div :class="['text-[16px] font-medium leading-[24px] text-[#1C1C1E]', titleClass]">
      <slot name="title">{{ title }}</slot>
    </div>

    <!-- 下载按钮区域 -->
    <div v-if="showDownload">
      <slot name="download" :handleDownloadClick="handleDownloadClick">
        <div
          :class="[
            'py-[4px] px-[8px] bg-[rgba(0,133,227,0.08)] rounded-[12px] text-[11px] text-[#0071E3] font-medium leading-[16px] min-w-[76px] flex justify-center items-center cursor-pointer hover:bg-[rgba(0,133,227,0.12)] transition-colors',
            downloadClass,
          ]"
          @click="handleDownloadClick"
        >
          <slot name="download-icon">
            <img
              src="./assets/download-icon.png"
              alt="download"
              class="w-[14px] h-[14px] block mr-[2px]"
            />
          </slot>
          <span>{{ $t(downloadText) }}</span>
        </div>
      </slot>
    </div>

    <!-- 自定义操作区域 -->
    <div v-if="!showDownload">
      <slot name="actions" />
    </div>
  </div>
</template>

<style scoped>
/* 如果需要额外的样式可以在这里添加 */
</style>
