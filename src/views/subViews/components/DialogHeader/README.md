# DownloadHeader 组件

一个通用的下载头部组件，支持标题显示和下载功能。

## 功能特性

- 📝 **灵活的标题显示** - 支持 props 和 slot 两种方式
- 📥 **下载按钮** - 内置下载图标和点击事件
- 🎨 **高度可定制** - 支持自定义样式和完全自定义的 slot
- 🔗 **事件透出** - 支持下载点击事件的透出
- 🎯 **响应式设计** - 包含 hover 效果和过渡动画

## 基础用法

```vue
<template>
  <DownloadHeader title="库存提醒" @download-click="handleDownloadClick" />
</template>

<script setup lang="ts">
import { DownloadHeader } from "./components";

const handleDownloadClick = (event: MouseEvent) => {
  console.log("开始下载...");
  // 实现下载逻辑
};
</script>
```

## Props

| 属性            | 类型      | 默认值       | 说明                                   |
| --------------- | --------- | ------------ | -------------------------------------- |
| `title`         | `string`  | `""`         | 标题文本，当没有使用 title slot 时显示 |
| `downloadText`  | `string`  | `"下载明细"` | 下载按钮文本                           |
| `downloadIcon`  | `string`  | `"download"` | 下载按钮图标名称                       |
| `showDownload`  | `boolean` | `true`       | 是否显示下载按钮                       |
| `downloadClass` | `string`  | `""`         | 下载按钮自定义样式类                   |
| `titleClass`    | `string`  | `""`         | 标题自定义样式类                       |

## 事件

| 事件名           | 参数                | 说明             |
| ---------------- | ------------------- | ---------------- |
| `download-click` | `event: MouseEvent` | 下载按钮点击事件 |

## Slots

### title slot

自定义标题内容：

```vue
<DownloadHeader @download-click="handleDownloadClick">
  <template #title>
    <div class="flex items-center">
      <Icon name="warning" class="mr-2" />
      <span>库存提醒</span>
    </div>
  </template>
</DownloadHeader>
```

### download slot

完全自定义下载按钮：

```vue
<DownloadHeader title="标题">
  <template #download="{ handleDownloadClick }">
    <button 
      class="custom-download-btn"
      @click="handleDownloadClick"
    >
      <Icon name="export" />
      导出数据
    </button>
  </template>
</DownloadHeader>
```

### download-icon slot

自定义下载图标：

```vue
<DownloadHeader title="标题" @download-click="handleDownloadClick">
  <template #download-icon>
    <Icon name="export" class="w-3 h-3 mr-1" />
  </template>
</DownloadHeader>
```

### actions slot

当不显示下载按钮时，自定义操作区域：

```vue
<DownloadHeader title="标题" :show-download="false">
  <template #actions>
    <button class="btn-primary">保存</button>
    <button class="btn-secondary">取消</button>
  </template>
</DownloadHeader>
```

## 高级用法

### 自定义样式

```vue
<DownloadHeader
  title="库存提醒"
  download-text="导出Excel"
  title-class="text-lg font-bold text-blue-600"
  download-class="bg-green-100 text-green-600 hover:bg-green-200"
  @download-click="handleDownloadClick"
/>
```

### 条件显示下载按钮

```vue
<DownloadHeader
  :title="dialogTitle"
  :show-download="hasDownloadPermission"
  @download-click="handleDownloadClick"
/>
```

### 结合现有项目的组件

```vue
<DownloadHeader @download-click="handleDownloadClick">
  <template #title>
    <div class="flex items-center">
      <DmpIcon name="health-diagnosis" class="mr-2" />
      <span>健康诊断</span>
    </div>
  </template>
  
  <template #download-icon>
    <DmpIcon name="download" class="w-3 h-3 mr-1" />
  </template>
</DownloadHeader>
```

## 实际应用示例

在 `InventoryWarningDialog.vue` 中的使用：

```vue
<script setup lang="ts">
import { DownloadHeader } from "../components";

// 处理下载点击事件
const handleDownloadClick = (event: MouseEvent) => {
  console.log("开始下载库存提醒数据:", {
    cycle: currentCycle.value,
    lob: currentLob.value,
    stats: currentLobStats.value,
  });

  const filename = `库存提醒_${currentLob.value}_${currentCycle.value}_${new Date().toISOString().split("T")[0]}.xlsx`;
  console.log(`下载完成: ${filename}`);
};
</script>

<template>
  <div class="flex flex-col gap-[8px]">
    <DownloadHeader title="库存提醒" @download-click="handleDownloadClick" />
    <!-- 其他内容 -->
  </div>
</template>
```

## 注意事项

1. **事件处理**：组件只负责触发 `download-click` 事件，具体的下载逻辑需要在父组件中实现
2. **样式定制**：可以通过 `titleClass` 和 `downloadClass` props 来自定义样式
3. **图标替换**：可以通过 `download-icon` slot 来替换默认的下载图标
4. **完全自定义**：通过 `download` slot 可以完全自定义下载按钮的外观和行为
