import { useConfigStore } from "@/stores/config";
import dayjs from "dayjs";
import type { Cycle } from "@dmp/components";

export type CycleItem = Cycle[number];

export const useCycleSelector = (
  moduleName: string,
  isWeek: boolean = true
): {
  cycles: Ref<Cycle>;
  current: Ref<CycleItem>;
  currentWeek: Ref<CycleItem>;
} => {
  const {
    getUpdateTimeByModule,
    getFormattedFiscalWeeks,
    getCurrentWeek,
    getFormattedFiscalDays,
    getCurrentDay,
  } = useConfigStore();
  const current: Ref<CycleItem> = ref(null);

  const updateTime: Ref<number | string> = computed(() => {
    if (isWeek) {
      return +(getUpdateTimeByModule[moduleName]?.fiscal_week_year ?? 999999);
    }
    return getUpdateTimeByModule[moduleName]?.fiscal_dt ?? "";
  });

  const cycles: Ref<Cycle> = computed(() => {
    let filtered: Cycle = [];
    const original = isWeek ? getFormattedFiscalWeeks : getFormattedFiscalDays;

    if (isWeek) {
      filtered = getFormattedFiscalWeeks.filter(
        (item: CycleItem) => +item.value <= +updateTime.value
      );
    } else {
      filtered = getFormattedFiscalDays.filter((item: CycleItem) =>
        updateTime.value
          ? dayjs(item.value).isBefore(dayjs(updateTime.value)) ||
            dayjs(item.value).isSame(dayjs(updateTime.value))
          : true
      );
    }

    return filtered.length ? filtered : original;
  });

  const currentWeek = computed(() => {
    if (isWeek) {
      return getCurrentWeek;
    }
    return getCurrentDay;
  });

  watchEffect(() => {
    current.value = cycles.value?.[cycles.value.length - 1] ?? null;
  });

  return { cycles, current, currentWeek };
};
