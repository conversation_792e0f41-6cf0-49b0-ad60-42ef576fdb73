<script setup lang="ts">
defineOptions({
  name: "BasicDialog",
});

type BasicDialogProps = {
  showLine?: boolean;
  headerClass?: string;
  bodyClass?: string;
  lineClass?: string;
};

const props = withDefaults(defineProps<BasicDialogProps>(), {
  showLine: true,
  headerClass: "",
  bodyClass: "",
  lineClass: "",
});

const isMobile = inject<Ref<boolean>>("isMobile");
</script>

<template>
  <div
    class="basic-dialog pt-[20px] pl-[20px] pr-[8px] box-border flex flex-col overflow-hidden"
    :class="[isMobile ? 'mobile-height' : 'h-[610px]']"
  >
    <div
      class="basic-dialog-header box-border pr-[12px] flex flex-col gap-[8px]"
      :class="props.headerClass"
    >
      <slot name="header" />
      <div
        class="w-full h-[1px] bg-[rgba(28,28,30,0.08)]"
        v-if="props.showLine"
        :class="props.lineClass"
      />
    </div>
    <div
      class="basic-dialog-body relative flex-1 min-h-[0px] box-border pb-[20px] pr-[8px]"
      :class="props.bodyClass"
    >
      <slot name="default" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.basic-dialog {
  border-radius: inherit;
}

.mobile-height {
  height: calc(var(--vh, 1vh) * 100 - 44px);
}

.basic-dialog-body {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(28, 28, 30, 0.06);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(28, 28, 30, 0.12);
  }
}

@supports (scrollbar-gutter: stable) {
  .basic-dialog-body {
    scrollbar-gutter: stable;
    overflow-y: auto;
  }
}
@supports not (scrollbar-gutter: stable) {
  .basic-dialog-body {
    overflow-y: scroll;
  }
}
</style>
