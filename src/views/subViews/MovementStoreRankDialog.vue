<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getStoreRankDialogInfo, getStoreRankDownload } from "@/api/index";
import { formatMovementStoreRank } from "@/intermediates/format/formatStoreRankDialog";
import type { PopoverRankItem } from "@dmp/components";
import UpSvg from "@/assets/icons/rank/up.svg";
import DownSvg from "@/assets/icons/rank/down.svg";
import { DialogHeader } from "./components/DialogHeader";
import BasicDialog from "./components/BasicDialog.vue";
import { useCycleSelector } from "./components/useCycleSelector";
import { downloadFile } from "@/utils/downloadFile";
const { lob, moduleName } = defineProps<{ lob: string; moduleName: string }>();
const { t } = useI18n();
const { cycles, current, currentWeek } = useCycleSelector(moduleName);

// api
const rankData: Ref<PopoverRankItem[]> = ref([]);
const loading = ref(false);
const currentCycle = ref(current.value?.value ?? null);

watchEffect(() => {
  loading.value = true;
  getStoreRankDialogInfo({ metric: lob, fiscal_week_year: currentCycle.value })
    .then(({ data = [] }) => {
      rankData.value = formatMovementStoreRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});
const dialogHeaderTitle = computed(() => {
  return lob ? `${t(lob)}${t("门店排名")}` : t("门店排名");
});
const handleDownload = async () => {
  try {
    const res = await getStoreRankDownload({
      fiscal_week_year: currentCycle.value,
    });
    try {
      downloadFile(res);
    } catch (error) {
      ElMessage.error("下载失败：" + (error as Error).message);
    }
  } catch (error) {
    ElMessage.error("请求失败：" + (error as Error).message);
  }
};
</script>

<template>
  <BasicDialog :body-class="'flex flex-col mt-[12px]'">
    <template #header>
      <DialogHeader :title="dialogHeaderTitle" @download-click="handleDownload" />
      <DmpCycleSelector v-model="currentCycle" :cycles="cycles" :current-week="currentWeek.value" />
    </template>
    <template #default>
      <DmpPopoverRank
        class="flex flex-col flex-1"
        :name-title="t('排名')"
        :value-title="t('较上周排名')"
        :columns="['1fr', '70px']"
        :list="rankData"
        :loading="loading"
      >
        <template v-slot:value="{ row }">
          <div
            class="flex items-center justify-start gap-[2px] text-[16px] leading-[16px] text-[#1C1C1E] font-semibold"
          >
            <UpSvg v-if="row?.diffType === 1" />
            <span
              class="w-[9px] h-[2px] bg-[#AEAEB2] rounded-[2px]"
              v-else-if="row?.diffType === 0"
            />
            <DownSvg v-else-if="row?.diffType === -1" />

            <span v-if="row?.value">{{ row.value }}</span>
          </div>
        </template>
      </DmpPopoverRank>
    </template>
  </BasicDialog>
</template>

<style lang="scss" scoped></style>
