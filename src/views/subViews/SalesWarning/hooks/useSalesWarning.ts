import {
  getSalesAlertDialogInfo,
  getSalesAlertTrendInfo,
  getSalesWarningDownload,
} from "@/api/index";

import { SalesWarningResponse, SalesWarningTrendItem } from "@/api/interface/SalesWarning";
import { useConfigStore } from "@/stores/config";
import { downloadFile } from "@/utils/downloadFile";
import { useCycleSelector, type CycleItem } from "../../components/useCycleSelector";
import { fiscal2Number } from "@/utils/util";
import { useI18n } from "vue-i18n";

const configStore = useConfigStore();
export function useSalesWarning(moduleName: string) {
  const { t } = useI18n();
  const { cycles, current, currentWeek } = useCycleSelector(moduleName, false);
  const { current: currentWeekYear } = useCycleSelector(moduleName);
  // 状态管理
  const dates = ref<CycleItem[]>(cycles.value);
  const currentCycle = ref<string | number>(current.value?.value ?? "");
  const salesAlertData = ref<SalesWarningResponse[]>([]);
  const chartsXAxisData = ref<string[]>([]);
  const chartsSeries = ref<{ name: string; data: number[] }[]>([]);
  const loading = ref(false); // 预警对话框信息loading
  const chartsLoading = ref(false); // 预警趋势信息loading
  const currentLob = ref<string>("");

  const unitMap: Record<string, string> = {
    warning_times: "次",
    warning_store: "家",
  };

  // 根据选中日期过滤出当天的预警LOB数据
  const currentDateData = computed(() => {
    if (!salesAlertData.value.length || !currentCycle.value) return null;
    return salesAlertData.value.find((item) => item.date === currentCycle.value);
  });

  // 动态生成tabs数据
  const tabs = computed(() => {
    // 确保配置加载完成后再获取tabs
    if (!configStore.isConfigLoaded) {
      return [];
    }
    return configStore.getSalesWarningTabs;
  });

  const hideTabs = computed(() => {
    return tabs.value.length <= 1;
  });

  // 根据选中的LOB获取对应的levels数据
  const tableData = computed(() => {
    if (!currentDateData.value?.warning_lobs?.length || !currentLob.value) return [];

    const selectedLobData = currentDateData.value.warning_lobs.find(
      (item) => item.lob === currentLob.value
    );

    if (!selectedLobData?.levels) return [];

    return selectedLobData.levels.map((level) => ({
      level: getLevelDisplayName(level.level),
      warning_times: level.warn_cnt.toString(),
      warning_store: level.pos_cnt.toString(),
    }));
  });

  // 表格列配置
  const columns = computed<{ label: string; prop: string }[]>(() => [
    {
      label: "",
      prop: "level",
      width: 136,
    },
    {
      label: t("预警次数"),
      prop: "warning_times",
      width: 96,
    },
    {
      label: t("预警门店"),
      prop: "warning_store",
      width: 88,
    },
  ]);

  const getLevelDisplayName = (level: string) => {
    const levelMap = {
      very_severe: t("非常严重"),
      severe: t("严重"),
      medium: t("中等"),
    };
    return levelMap[level] || level;
  };

  // 处理tab切换
  const handleTabChange = (lobValue: string) => {
    currentLob.value = lobValue;
  };

  // 加载销售预警对话框信息
  const loadSalesAlertDialogInfo = async () => {
    loading.value = true;
    try {
      const res = await getSalesAlertDialogInfo({
        fiscal_dt: currentCycle.value,
      });

      if (res.data && res.code === 0 && Array.isArray(res.data)) {
        salesAlertData.value = res.data;
      }
    } catch (error) {
      console.log(error, 7867868768);
    } finally {
      loading.value = false;
    }
  };

  // 加载销售预警趋势信息
  const loadSalesAlertTrendInfo = async () => {
    chartsLoading.value = true;
    try {
      const res = await getSalesAlertTrendInfo({
        fiscal_week_year: currentWeekYear.value?.value,
      });
      if (res.data && Array.isArray(res.data)) {
        const sortedData = res.data.sort((a, b) => {
          return fiscal2Number(a.fiscal_qtr_week_name) - fiscal2Number(b.fiscal_qtr_week_name);
        });
        // 提取 week_name 作为 x 轴数据
        chartsXAxisData.value = sortedData.map((item: SalesWarningTrendItem) => item.week_name);

        // 构建系列数据
        const veryServerData = sortedData.map(
          (item: SalesWarningTrendItem) => item.very_severe || 0
        );
        const severeData = sortedData.map((item: SalesWarningTrendItem) => item.severe || 0);
        const mediumData = sortedData.map((item: SalesWarningTrendItem) => item.medium || 0);

        chartsSeries.value = [
          { name: "非常严重", data: veryServerData },
          { name: "严重", data: severeData },
          { name: "中等", data: mediumData },
        ];
      }
    } catch (error) {
      console.log(error, 7867868768);
    } finally {
      chartsLoading.value = false;
    }
  };

  // 初始化数据
  const initData = () => {
    loadSalesAlertDialogInfo();
    loadSalesAlertTrendInfo();
  };

  const handleDownloadClick = async () => {
    const res = await getSalesWarningDownload({
      date: currentCycle.value,
    });
    downloadFile(res);
  };

  // 监听日期变化和配置加载，设置第一个可用的LOB
  watch(
    [currentCycle, tabs, () => configStore.isConfigLoaded],
    () => {
      // 当日期变化或tabs变化或配置加载完成时，自动选择第一个可用的LOB
      if (tabs.value.length > 0 && !currentLob.value) {
        currentLob.value = tabs.value[0].value as string;
      } else if (tabs.value.length === 0 && configStore.isConfigLoaded) {
        // 配置已加载但tabs为空，说明没有配置或配置为空
        currentLob.value = "";
      }
    },
    { immediate: true }
  );

  return {
    // 状态
    dates,
    currentCycle,
    currentWeek: currentWeek.value.value,
    salesAlertData,
    currentLob,
    chartsXAxisData,
    chartsSeries,
    loading,
    chartsLoading,
    hideTabs,
    // 计算属性
    currentDateData,
    tabs,
    tableData,
    columns,
    unitMap,
    // 方法
    handleTabChange,
    loadSalesAlertDialogInfo,
    loadSalesAlertTrendInfo,
    initData,
    handleDownloadClick,
  };
}
