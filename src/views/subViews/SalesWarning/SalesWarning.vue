<!--
 * @Date: 2025-07-22 15:28:28
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-28 10:27:15
 * @FilePath: /MyBusinessV2/src/views/subViews/SalesWarning/SalesWarning.vue
-->
<script setup lang="ts">
import { useSalesWarning } from "./hooks/useSalesWarning";
import { DialogHeader } from "../components/DialogHeader";
import BasicDialog from "../components/BasicDialog.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 定义 emits
const emit = defineEmits(["update:currentCycle"]);

const {
  dates,
  currentCycle,
  currentWeek,
  chartsXAxisData,
  chartsSeries,
  loading,
  chartsLoading,
  tabs,
  tableData,
  columns,
  unitMap,
  hideTabs,
  handleTabChange,
  initData,
  handleDownloadClick,
} = useSalesWarning("healthDiagnosis");
const firstRender = ref(true);
onMounted(() => {
  initData();
});
const handleCycleChange = () => {
  firstRender.value = false;
};
const title = computed(() => {
  return tabs.value.length === 1 ? `${tabs.value[0].label} ${t("销售预警")}` : t("销售预警");
});
</script>

<template>
  <BasicDialog>
    <template #header>
      <DialogHeader :title="title" @download-click="handleDownloadClick" />
      <DmpTextList
        :texts="[
          t('各产品线因连续未销售而触发的销售预警次数。'),
          t('根据连续未售卖天数长短，分为“非常严重”，“严重”，“中等”。'),
        ]"
      />
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="dates"
        :current-week="currentWeek"
        @update:modelValue="(val) => emit('update:currentCycle', val)"
        @change="handleCycleChange"
      />
    </template>
    <template #default>
      <div class="flex flex-col gap-[16px] mt-[12px]">
        <dmp-tab-switcher
          v-if="!hideTabs"
          :tabs="tabs"
          :equal-width="true"
          @change="handleTabChange"
        />
        <div class="flex flex-col gap-[8px]">
          <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">
            {{ $t("预警分布") }}
          </h1>
          <dmp-detail-table
            :columns="columns"
            :data="tableData"
            :loading="loading"
            :unit-map="unitMap"
            :UnitPresetMode="['warning_times', 'warning_store']"
          />
        </div>
        <div class="flex flex-col gap-[8px]">
          <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E]">
            {{ $t("预警趋势") }}
          </h1>
          <dmp-bar-stack-chart
            subtitle="每周预警次数"
            :xAxisData="chartsXAxisData"
            :series="chartsSeries"
            :legendNames="['非常严重', '严重', '中等']"
            :loading="chartsLoading"
            :height-class="'!h-[174px]'"
            :first-render="firstRender"
          />
        </div>
      </div>
    </template>
  </BasicDialog>
</template>
