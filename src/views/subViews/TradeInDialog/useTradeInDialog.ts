import { useI18n } from "vue-i18n";
import { computed, type ComputedRef } from "vue";

interface TabConfig {
  label: string;
  value: string;
}

type LobType = "Trade-in" | "Setup" | "Data Transfer" | "MyCoach" | "ACPP" | "Financing";

type TabsMapType = Record<LobType, TabConfig[]>;

interface UseTradeInDialogReturn {
  tabsMap: ComputedRef<TabsMapType>;
  getTabsByLob: (lob: LobType) => TabConfig[] | undefined;
  getAllLobs: () => LobType[];
}

const getDefaultTradeInTabs = (lob: LobType, t: (str: string) => string) => [
  { label: `${t(lob)}${t("数量")}`, value: "count_list" },
  { label: `${t(lob)}${t("占比")}`, value: "proportion_list" },
];

export const useTradeInDialog = (lob?: LobType): UseTradeInDialogReturn => {
  const { t } = useI18n();

  const tabsMap: ComputedRef<TabsMapType> = computed(() => ({
    "Trade-in": getDefaultTradeInTabs(lob, t),
    Setup: getDefaultTradeInTabs(lob, t),
    "Data Transfer": getDefaultTradeInTabs(lob, t),
    MyCoach: [{ label: t("每周上课数量"), value: "count_list" }],
    ACPP: getDefaultTradeInTabs(lob, t),
    Financing: getDefaultTradeInTabs(lob, t),
  }));

  const getTabsByLob = (lob: LobType): TabConfig[] | undefined => {
    return tabsMap.value[lob];
  };

  const getAllLobs = (): LobType[] => {
    return Object.keys(tabsMap.value) as LobType[];
  };

  return {
    tabsMap,
    getTabsByLob,
    getAllLobs,
  };
};

// 导出类型供外部使用
export type { LobType, TabConfig, UseTradeInDialogReturn };
