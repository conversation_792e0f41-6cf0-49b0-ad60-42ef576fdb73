<!--
 * @Date: 2025-07-21 14:44:43
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-29 17:25:44
 * @FilePath: /MyBusinessV2/src/views/subViews/TradeInDialog.vue
-->
<script setup lang="ts">
import BasicDialog from "../components/BasicDialog.vue";
import { getTradeInInfo, getDatacenterTrendInfo, getTradeInDownload } from "@/api/index.js";
import { DialogHeader } from "../components/DialogHeader";
import { downloadFile } from "@/utils/downloadFile";
import { roundToOneDecimalStr, fiscal2Number } from "@/utils/util";
import { useI18n } from "vue-i18n";
import { TreadeInDetail, StatisticsInfo } from "@/api/interface/TradeInDialog";
import { formatNumber } from "@/utils/number";
import { useCycleSelector } from "../components/useCycleSelector";
import { useTradeInDialog, LobType } from "./useTradeInDialog";
// 类型定义
interface Tab {
  label: string;
  value: string;
}
interface ChartDataProp {
  fiscal_qtr_week_name: string;
  count_list: { fiscal_week: string; value: number }[];
  proportion_list: { fiscal_week: string; value: number }[];
}
const { t } = useI18n();
const { lob, moduleName } = defineProps<{ lob?: string; moduleName?: string }>();
const { cycles, current, currentWeek } = useCycleSelector(moduleName);
const currentCycle = ref<string | number | null>(current.value?.value ?? null);
const currentTab = ref<string>("count_list");
const statisticsList = ref<StatisticsInfo[]>([]);
const xAxisData = ref<string[]>([]);
const chartData = ref<ChartDataProp[]>([]);
const loading = ref(true);
const series = ref<{ data: number[] }[]>([]);
const state = ref<TreadeInDetail[]>([]);
const firstRender = ref(true);
const { getTabsByLob } = useTradeInDialog(lob as LobType);

const tabs = computed((): Tab[] => {
  return getTabsByLob(lob as LobType) || [];
});

const onlyOneTab = computed(() => {
  return tabs.value.length <= 1;
});

const title = computed(() => {
  const lobName = lob === "Trade-in" || lob === "Data Transfer" ? "iPhone" : "";
  return `${lobName} ${t("详情")}`;
});

const currentLabel = computed(() => {
  return cycles.value.find((item) => item.value === currentCycle.value)?.label;
});

const unitMap: Record<string, string> = {
  "Trade-in": "Trade-in",
  Setup: "Setup",
  "Data Transfer": "Data Transfer",
  MyCoach: "MyCoach",
  ACPP: "ACPP",
  Financing: "Financing",
};
const retailUnit = {
  Financing: "单",
  Setup: "个",
  MyCoach: "节",
  "Trade-in": "个",
  "Data Transfer": "次",
  ACPP: "片",
};

const handleDownload = async () => {
  try {
    const res = await getTradeInDownload({
      fiscal_week_year: currentCycle.value,
      type: unitMap[lob],
    });
    try {
      downloadFile(res);
    } catch (error) {
      ElMessage.error("下载失败：" + (error as Error).message);
    }
  } catch (error) {
    ElMessage.error("请求失败：" + (error as Error).message);
  }
};

const handleChange = (val: string) => {
  const found = chartData.value.find((item) => item.fiscal_qtr_week_name === currentLabel.value);
  if (found) {
    const echartData = (found[val] as any[]).sort((a, b) => {
      return fiscal2Number(a.fiscal_qtr_week_name) - fiscal2Number(b.fiscal_qtr_week_name);
    });
    xAxisData.value = echartData.map((item) => item.fiscal_week);
    series.value = [{ data: echartData.map((item) => item.value) }];
  } else {
    xAxisData.value = [];
    series.value = [];
  }
  currentTab.value = val;
};

const handleCycleChange = () => {
  firstRender.value = false;

  const found = state.value.find((item) => item.fiscal_qtr_week_name === currentLabel.value);
  if (found) {
    statisticsList.value = found.statisticsList;
    statisticsList.value.map((item) => {
      if (item.value_type === "number") {
        item.unit = item.name === "上週成交店數" ? "家" : retailUnit[lob];
        item.value = formatNumber(item.value);
      } else if (
        item.value != null &&
        typeof item.value === "string" &&
        !item.value.endsWith("%")
      ) {
        item.value = roundToOneDecimalStr(item.value, 0);
        item.unit = "%";
      }
    });
  } else {
    statisticsList.value = [];
  }
  handleChange(currentTab.value);
};

onMounted(() => {
  Promise.all([
    getTradeInInfo({
      "request-type": unitMap[lob],
      lob: lob === "Trade-in" || lob === "Data Transfer" ? "iPhone" : "Total",
      fiscal_week_year: currentCycle.value,
    }),
    getDatacenterTrendInfo({
      "request-type": unitMap[lob],
      lob: lob === "Trade-in" || lob === "Data Transfer" ? "iPhone" : "Total",
      fiscal_week_year: currentCycle.value,
    }),
  ])
    .then(([baseRes, echartRes]) => {
      state.value = baseRes.data;
      chartData.value = echartRes.data;
      handleCycleChange();
    })
    .catch((error) => {
      console.error("请求失败:", error);
    })
    .finally(() => {
      loading.value = false;
    });
});
</script>

<template>
  <BasicDialog>
    <template #header>
      <DialogHeader :title="$t(lob)" @download-click="handleDownload" />
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="cycles"
        :current-week="currentWeek.value"
        @change="handleCycleChange"
      />
    </template>
    <template #default>
      <dmp-info-detail-table
        class="my-[16px]"
        :items="statisticsList"
        :title="title"
        :loading="loading"
      />
      <div class="flex flex-col">
        <h1 class="text-[13px] font-medium leading-[20px] text-[#1C1C1E] mb-2">
          {{ `${$t(lob)}${$t("每周趋势")}` }}
        </h1>

        <div
          class="mb-[12px] text-[12px] font-regular text-[#1C1C1E] leading-[16px]"
          v-if="onlyOneTab"
        >
          {{ tabs[0].label }}
        </div>
        <dmp-tab-switcher
          v-if="!onlyOneTab"
          :tabs="tabs"
          :equal-width="true"
          :defaultTab="currentTab"
          @change="handleChange"
        />
        <dmp-line-chart
          :xAxisData="xAxisData"
          :series="series"
          :loading="loading"
          :first-render="firstRender"
        />
      </div>
    </template>
  </BasicDialog>
</template>
