<script setup lang="ts">
import { getScoreDetailsDialogInfo } from "@/api/index.js";
import { DialogHeader } from "./components/DialogHeader";
import {
  HealthDiagnosisDialogItem,
  HealthDiagnosisDialogMetric,
} from "@/api/interface/HealthDiagnosisDialog";
import { useI18n } from "vue-i18n";
import BasicDialog from "./components/BasicDialog.vue";
import { useCycleSelector } from "./components/useCycleSelector";
import { fiscal2Number } from "@/utils/util";

const { moduleName } = defineProps<{ moduleName?: string }>();
const { t } = useI18n();
const { cycles, current, currentWeek } = useCycleSelector(moduleName);

interface Tab {
  label: string;
  value: string;
}
interface Column {
  label: string;
  prop: string;
  width?: number;
}

const currentCycle: Ref<string | number | null> = ref(current.value?.value ?? null);
const healthData = ref<HealthDiagnosisDialogMetric[]>([]);
const xAxisData = ref<string[]>([]);
const series = ref<{ data: number[] }[]>([]);
const state = ref<HealthDiagnosisDialogItem[]>([]);
const firstRender = ref(true);
const columns = computed((): Column[] => [
  { label: t("指标"), prop: "cn_name", width: 144 },
  { label: t("满分"), prop: "total_score", width: 88 },
  { label: t("得分"), prop: "score", width: 88 },
]);

const tabs = computed((): Tab[] => [
  { label: t("整体"), value: "total_week_trend" },
  { label: t("销售预警"), value: "so_warning_week_trend" },
  { label: t("库存预警"), value: "inv_warning_week_trend" },
  { label: t("库存提醒"), value: "inv_remind_week_trend" },
]);
const currentTab = ref(tabs.value[0].value);

const unitMap: Record<string, string> = {
  total_score: t("分"),
  score: t("分"),
};

const currentState = computed(() => {
  const { label = "" } = cycles.value.find((item) => item.value === currentCycle.value);
  return state.value.find((item) => item.fiscal_qtr_week_name === label);
});

const handleCycleChange = () => {
  firstRender.value = false;
  healthData.value = currentState.value ? (currentState.value?.metrics ?? []) : [];
  handleChange(currentTab.value);
};

// 后期替换 v-model
const handleChange = (val: string) => {
  if (currentState.value) {
    const echartData = (currentState.value[val] as any[]).sort((a, b) => {
      return fiscal2Number(a.fiscal_qtr_week_name) - fiscal2Number(b.fiscal_qtr_week_name);
    });
    xAxisData.value = echartData.map((item) => item.week_name);
    series.value = [{ data: echartData.map((item) => item.score) }];
  } else {
    xAxisData.value = [];
    series.value = [];
  }
  currentTab.value = val;
};

const loading = ref(true);
const getBaseInfo = async () => {
  const res = await getScoreDetailsDialogInfo({
    fiscal_week_year: currentCycle.value,
  });
  state.value = res.data;
  handleCycleChange();
  loading.value = false;
};

onMounted(() => {
  getBaseInfo();
});
</script>

<template>
  <BasicDialog>
    <template #header>
      <DialogHeader :title="t('健康诊断')" :showDownload="false" />
      <dmp-cycle-selector
        v-model="currentCycle"
        :cycles="cycles"
        :current-week="currentWeek.value"
        @change="handleCycleChange"
      />
    </template>
    <template #default>
      <dmp-detail-table
        class="my-[16px]"
        :columns="columns"
        :data="healthData"
        :title="t('得分详情')"
        :unitMap="unitMap"
        :highlightRule="(row, col) => col.prop === 'score' && row.score < row.total_score"
        :loading="loading"
        :height-class="'!h-[128px]'"
        :empty-text="t('暂无数据')"
        :UnitPresetMode="['total_score', 'score']"
      />
      <div class="flex flex-col">
        <h1 class="text-[14px] font-medium leading-[20px] text-[#1C1C1E] mb-[8px]">
          {{ t("健康度趋势") }}
        </h1>
        <dmp-tab-switcher
          :tabs="tabs"
          :equal-width="true"
          :defaultTab="currentTab"
          @change="handleChange"
        />
        <dmp-line-chart
          title=""
          subtitle="每周健康分数"
          :xAxisData="xAxisData"
          :series="series"
          :loading="loading"
          :height-class="'!h-[174px]'"
          :first-render="firstRender"
        />
      </div>
    </template>
  </BasicDialog>
</template>
