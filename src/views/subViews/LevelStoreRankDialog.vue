<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { getStoreRankDialogInfo, getStoreRankDownload } from "@/api/index";
import { formatLevelStoreRank } from "@/intermediates/format/formatStoreRankDialog";
import type { PopoverRankItem } from "@dmp/components";
import { DialogHeader } from "./components/DialogHeader";
import BasicDialog from "./components/BasicDialog.vue";
import { useCycleSelector } from "./components/useCycleSelector";
import { downloadFile } from "@/utils/downloadFile";
const { lob, moduleName, lobzh } = defineProps<{
  lob: string;
  moduleName: string;
  lobzh: string;
}>();
const { t } = useI18n();
const { cycles, current, currentWeek } = useCycleSelector(moduleName);

// tab
const tabOptions = ref([
  { label: "Level A", value: "Level A" },
  { label: "Level B", value: "Level B" },
  { label: "Level C", value: "Level C" },
  { label: "Level D", value: "Level D" },
  { label: "Level E", value: "Level E" },
]);
const tabValue = ref(tabOptions.value[0].value);
const dialogHeaderTitle = computed(() => {
  return lobzh ? `${t(lobzh)} ${t("门店排名")}` : t("门店排名");
});
const currentCycle = ref(current.value?.value ?? null);

// api
const rankData: Ref<PopoverRankItem[]> = ref([]);
const loading = ref(false);
watchEffect(() => {
  loading.value = true;
  getStoreRankDialogInfo({ metric: lob, tab: tabValue.value, fiscal_week_year: currentCycle.value })
    .then(({ data = [] }) => {
      rankData.value = formatLevelStoreRank(data);
    })
    .catch((error) => {
      console.warn(error);
    })
    .finally(() => {
      loading.value = false;
    });
});
const handleDownload = async () => {
  try {
    const res = await getStoreRankDownload({
      fiscal_week_year: currentCycle.value,
    });
    try {
      downloadFile(res);
    } catch (error) {
      ElMessage.error("下载失败：" + (error as Error).message);
    }
  } catch (error) {
    ElMessage.error("请求失败：" + (error as Error).message);
  }
};
</script>

<template>
  <BasicDialog :show-line="false" :body-class="'flex flex-col gap-[12px] mt-[12px]'">
    <template #header>
      <DialogHeader :title="dialogHeaderTitle" @download-click="handleDownload" />
      <DmpCycleSelector v-model="currentCycle" :cycles="cycles" :current-week="currentWeek.value" />
      <div class="w-full h-[1px] bg-[rgba(28,28,30,0.08)] my-[4px]" />
      <DmpTabSwitcher :tabs="tabOptions" v-model:defaultTab="tabValue" equalWidth />
    </template>
    <template #default>
      <DmpPopoverRank
        class="flex flex-col flex-1"
        :name-title="t('排名')"
        :value-title="t('分数')"
        :columns="['1fr', '75px']"
        :list="rankData"
        :loading="loading"
      >
        <template v-slot:value="{ row }">
          <DmpValueWithUnit
            :value="row?.value"
            :unit="t('分')"
            :value-class="['!text-[16px]', '!text-[#3948B1]', '!font-semibold']"
            :unit-class="['!text-[12px]', '!text-[#3948B1]', '!leading-[14px]', '!font-semibold']"
          />
        </template>
      </DmpPopoverRank>
    </template>
  </BasicDialog>
</template>
