<script setup lang="ts">
import loadingData from "../assets/lottie/loading.json";
</script>

<template>
  <div class="min-h-screen flex items-center justify-center p-4">
    <div
      class="w-[560px] h-[224px] flex flex-col items-center justify-center text-center glass-card"
    >
      <dmp-lottie-player
        :animation-data="loadingData"
        :width="56"
        :height="56"
        class="mb-2"
        :loop="true"
        :autoplay="true"
      />
      <h3 class="text-[20px] font-medium text-[#1C1C1E] mb-2 leading-[32px]">
        {{ $t("正在验证权限") }}
      </h3>
      <h4 class="text-[16px] text-[#6E6E73] font-normal leading-[20px]">
        {{ $t("请稍后，系统正在验证您的访问权限") }}
      </h4>
    </div>
  </div>
</template>
