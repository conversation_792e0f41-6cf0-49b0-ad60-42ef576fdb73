<script setup lang="ts">
import IconNoPermission from "@/assets/icon-no-permission.svg";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
</script>

<template>
  <div class="min-h-screen flex items-center justify-center p-4">
    <div
      class="w-[560px] h-[224px] flex flex-col items-center justify-center text-center"
      style="
        border-radius: 24px;
        backdrop-filter: blur(24px) saturate(140%);
        -webkit-backdrop-filter: blur(24px) saturate(140%);
        background: rgba(255, 255, 255, 0.14);
        box-shadow:
          -1px -1px 2px 0px rgba(255, 255, 255, 0.32) inset,
          1px 1px 2px 0px rgba(255, 255, 255, 0.32) inset,
          1px 1px 4px 0px rgba(0, 0, 0, 0.08) inset;
        transition:
          transform 0.3s ease,
          box-shadow 0.3s ease;
        will-change: transform;
        transform: translateZ(0);
      "
    >
      <IconNoPermission class="w-[56px] h-[56px] mb-3" />
      <h3 class="text-[20px] font-medium text-[#1C1C1E] mb-2 leading-[32px]">
        {{ t("暂无权限") }}
      </h3>
      <h4 class="text-[16px] text-[#6E6E73] font-normal leading-[20px] px-[50px]">
        {{ t("当前账号暂无访问权限，请联系团队负责人申请") }}
      </h4>
    </div>
  </div>
</template>
