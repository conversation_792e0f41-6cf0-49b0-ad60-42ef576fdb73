<script setup lang="ts">
import DashboardCenter from "@/views/components/DashboardCenter/DashboardCenter.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const gridTemplates = {
  desktop: {
    col: 6,
  },
  mobile: {
    col: 2,
  },
};

const gridSizes = {
  desktop: {
    row: 152,
    col: 165,
  },
  mobile: {
    row: 152,
    col: 165,
  },
};
</script>

<template>
  <DashboardCenter id="dataCenter" title="数据中心" icon="数据中心" :grid-templates :grid-sizes />
</template>

<style lang="scss" scoped>
:deep(.dmp-flex-card) {
  padding-inline: 8px;
  padding-bottom: 8px;

  .title {
    padding-inline: 8px;
  }
}
</style>
