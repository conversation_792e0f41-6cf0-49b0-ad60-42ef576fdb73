<script setup>
import DashboardCenter from "@/views/components/DashboardCenter/DashboardCenter.vue";
import { useI18n } from "vue-i18n";
import { useAppStore } from "@/stores/app.js";

const appStore = useAppStore();
const isMobile = computed(() => appStore.isMobile);
const mobileHeight = ref(544);

const { t } = useI18n();
const gridTemplates = {
  desktop: {
    col: 1,
    row: 1,
  },
  mobile: {
    col: 1,
    row: 1,
  },
};

const gridSizes = {
  desktop: {
    row: "472",
    col: "300",
  },
  mobile: {
    row: mobileHeight.value,
    col: "338",
  },
};
</script>

<template>
  <DashboardCenter
    id="healthDiagnosis"
    title="健康诊断"
    icon="健康诊断"
    class="w-full"
    :class="{ 'mobile-scroll': isMobile }"
    :style="{ '--mobile-height': `${mobileHeight}px` }"
    :grid-templates
    :grid-sizes
    :is-mobile="isMobile"
  />
</template>

<style lang="scss" scoped>
.mobile-scroll {
  :deep(.grid:has(> .dmp-health-diagnosis)) {
    max-height: var(--mobile-height);
  }
  :deep(.grid:has(> .dmp-health-diagnosis)),
  :deep(.dmp-health-diagnosis),
  :deep(.health-content),
  :deep(.dmp-health-diagnosis-alert) .content,
  :deep(.dmp-health-diagnosis-alert) .el-scrollbar {
    display: flex;
    flex-direction: column;
    min-height: 0;
  }
}
</style>
