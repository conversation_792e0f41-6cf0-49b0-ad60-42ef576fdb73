<script setup>
import DashboardHealthTitleIcon from "./assets/消息中心.svg";
import { useAppStore } from "@/stores";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const appStore = useAppStore();

const isMobile = computed(() => appStore.isMobile);
</script>

<template>
  <GlassCard
    :title="t('消息中心')"
    :description="t('暂无数据')"
    class="flex flex-col w-full justify-center"
    :icon="DashboardHealthTitleIcon"
  >
    <div class="flex items-center justify-center h-[116px] w-full">
      <ComingSoon>
        <div class="text-[12px] font-medium text-[rgba(28,28,28,.64)] leading-[32px]">
          {{ t("模块建设中") }}...
        </div>
      </ComingSoon>
    </div>
  </GlassCard>
</template>
