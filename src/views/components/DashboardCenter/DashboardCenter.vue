<script setup lang="ts">
import IntermediatesManager from "@/intermediates/intermediatesManager.ts";
import { name2CompMap } from "@/constants/schema/name2CompMap.ts";
import { useAppStore } from "@/stores/app.js";
import { computed, onMounted, provide, ref, watchEffect } from "vue";
import { useSchemaStore } from "@/stores/schema.ts";
import { DashboardCenterProps } from "@/views/components/DashboardCenter/props.ts";
import { debugError, debugLog } from "@/utils/util.ts";
import { CardSchema } from "../../../../packages/common/types";

const { id, title, updateTime, icon, gridTemplates, gridSizes } =
  defineProps<DashboardCenterProps>();

const appStore = useAppStore();
const schemaStore = useSchemaStore();
const isMobile = computed(() => appStore.isMobile);

provide("isMobile", isMobile);

const gridTemplate = computed(() =>
  isMobile.value ? gridTemplates.mobile : gridTemplates.desktop
);
const gridSize = computed(() => (isMobile.value ? gridSizes.mobile : gridSizes.desktop));
const contentSize = computed(() =>
  isMobile.value
    ? { width: "100%", height: "auto" }
    : {
        width: "100%",
        height: "auto",
      }
);
const dataList = ref([]);
const loading = ref(true);

const layoutComps = computed(
  () => schemaStore.schema?.layout.desktop.find((i) => i.id === id)?.children || []
);
const intermediatesManager = IntermediatesManager.instance;

watchEffect(async () => {
  dataList.value = await Promise.all(
    layoutComps.value.map(async (comp: CardSchema) => {
      try {
        const intermediate = intermediatesManager.get(comp.type, comp.api);
        if (!intermediate) return undefined;
        const { fetcher, formatter } = intermediate;
        return {
          data: await fetcher(),
          formatter,
        };
      } catch (e) {
        debugError(e);
        return undefined;
      }
    })
  );
  loading.value = false;
});
onMounted(() => {
  loading.value = true;
});
const layoutCompsReady = computed(() => {
  debugLog("Current Language: ", appStore.currentUseLang);
  return layoutComps.value.reduce(
    (
      prev: (CardSchema & {
        data: Record<string, any>;
      })[],
      comp: CardSchema,
      index: number
    ) => {
      const indexData = dataList.value[index];
      if (indexData) {
        const { data, formatter } = indexData;
        prev.push({
          ...comp,
          data: formatter(data),
        });
      }
      return prev;
    },
    []
  );
});
</script>

<template>
  <dmp-glass-card
    :title="title"
    :loading
    :grid-template
    :grid-size
    :content-size
    :disable-scrolling="isMobile"
    :icon="icon"
    :isMobile
  >
    <component
      v-for="comp in layoutCompsReady"
      :key="comp.title + comp.type"
      :id="comp.id"
      :title="comp.title"
      :is="name2CompMap[comp.type]"
      :update-time="updateTime"
      v-bind="comp.data"
      :isMobile="isMobile"
      empty-text="暂无数据"
    />
  </dmp-glass-card>
</template>

<style lang="scss" scoped>
:deep(.dmp-flex-card) {
  padding-inline: 8px;
  padding-bottom: 8px;

  .title {
    padding-inline: 8px;
  }
}
</style>
