<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { i18n, loadLanguageAsync } from "@/i18n";
import { useAppStore } from "@/stores/app.js";

const appStore = useAppStore();

const isMobile = computed(() => appStore.isMobile);

const { t } = useI18n();

interface Props {
  text?: string;
  showLinks?: boolean;
}
const { text = "每日数据预计将在12:00更新完毕，可能出现数据更新延迟。", showLinks = true } =
  defineProps<Props>();

function handleLinkClick() {
  window.open(
    "https://volumepurchaseprogramcredit.apple.com/itunes/html/tos/en_us/tos.html",
    "_blank"
  );
}
const selectOptions = ref([
  { label: "大陆 (简体中文)", value: "zh" },
  { label: "香港 (繁体中文)", value: "hk" },
  { label: "臺灣 (繁體中文)", value: "tw" },
  { label: "English", value: "en" },
]);
// 语言选择
const language = ref(i18n.global.locale || "zh");

const changeLanguage = (lang) => {
  loadLanguageAsync(lang, "manual"); // 标记为用户手动设置
};
</script>

<template>
  <footer class="flex-shrink-0 text-[12px] text-[#3A3A3C] font-normal py-[12px] mt-auto">
    <div
      v-if="showLinks"
      class="flex justify-between items-center"
      :class="isMobile ? 'flex-col' : ''"
    >
      <div>{{ t(text) }}</div>
      <div class="flex justify-center items-center gap-[16px] text-[#0077ED]">
        <!--        <span class="cursor-pointer hover:underline" @click="handleLinkClick('manual')">-->
        <!--          {{ t("使用手册") }}-->
        <!--        </span>-->
        <span class="cursor-pointer hover:underline" @click="handleLinkClick()">
          {{ t("使用条款") }}
        </span>
        <div class="flex flex-1 h-full item-center justify-center">
          <span class="mr-[8px] mt-[6px] text-[#1C1C1E]/10">|</span>
          <DmpSimpleDropdown
            placement="top"
            v-model="language"
            :options="selectOptions"
            btnClass="!text-[#0077ED] !font-normal !px-[12px] !py-[8px] !rounded-[12px]"
            dropdownClass="!bg-white/[0.64]"
            @change="changeLanguage"
          />
        </div>
      </div>
    </div>
    <div v-else>{{ text }}</div>
  </footer>
</template>
