<script setup>
import { useEventListener, useElementSize } from "@vueuse/core";
import { useAppStore } from "@/stores";

const appStore = useAppStore();

const isMobile = computed(() => appStore.isMobile);

defineProps({
  title: {
    type: String,
    default: "",
  },
  icon: {
    type: [String, Object],
    default: "✨",
  },
});

const contentRef = ref(null);
const isScrollable = ref(false);
const isAtStart = ref(true);
const isAtEnd = ref(false);

function updateScrollState() {
  const el = contentRef.value;
  if (!el) return;
  isScrollable.value = el.scrollWidth > el.clientWidth;
  isAtStart.value = el.scrollLeft <= 0;
  // 允许 1px 误差,避免浮点
  isAtEnd.value = el.scrollLeft + el.clientWidth >= el.scrollWidth - 1;
}

// 滚动到指定位置
function scrollByPage(direction) {
  const el = contentRef.value;
  if (!el) return;
  const distance = el.clientWidth * direction;
  el.scrollBy({ left: distance, behavior: "smooth" });
}

useElementSize(contentRef, updateScrollState);

let stopResizeListener, stopScrollListener;

onMounted(() => {
  nextTick(() => {
    updateScrollState();
    stopResizeListener = useEventListener(window, "resize", updateScrollState);
    stopScrollListener = useEventListener(contentRef, "scroll", updateScrollState);
  });
});

onUnmounted(() => {
  if (stopResizeListener) stopResizeListener();
  if (stopScrollListener) stopScrollListener();
});
</script>

<template>
  <div :class="{ 'glass-card': !isMobile }">
    <div v-if="title" class="flex items-center mb-[12px] ml-[8px]">
      <!-- 如果 icon 是组件，使用动态组件渲染 -->
      <component
        v-if="typeof icon === 'object'"
        :is="icon"
        class="w-[24px] h-[24px] mr-[4px] flex-shrink-0"
      />
      <!-- 如果 icon 是字符串，直接显示 -->
      <span v-else class="text-2xl mr-3">{{ icon }}</span>
      <h3 class="text-[16px] font-medium leading-[24px]">{{ title }}</h3>
    </div>

    <div
      ref="contentRef"
      :class="['glass-card-content', { 'glass-card bg-[rgba(255,255,255,.48)]': isMobile }]"
    >
      <slot />
    </div>

    <!-- <transition name="fade">
      <div v-if="isScrollable" class="absolute top-[20px] right-[24px] flex gap-[2px] z-10">
        <button
          @click="() => scrollByPage(-1)"
          :disabled="isAtStart"
          class="w-[24px] h-[24px] flex items-center justify-center rounded-full bg-white/70 hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed shadow"
          aria-label="向左滚动"
        >
          <svg
            class="w-[12px] h-[12px]"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        <button
          @click="() => scrollByPage(1)"
          :disabled="isAtEnd"
          class="w-[24px] h-[24px] flex items-center justify-center rounded-full bg-white/70 hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed shadow"
          aria-label="向右滚动"
        >
          <svg
            class="w-[12px] h-[12px]"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </transition> -->
  </div>
</template>

<style scoped>
.glass-card-content {
  overflow-x: auto;
  overflow-y: hidden;
  /* 隐藏滚动条样式 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.glass-card-content::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
