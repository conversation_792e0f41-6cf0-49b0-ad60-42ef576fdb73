<template>
  <div class="relative h-[40px]" :class="{ 'pointer-events-none opacity-50': props.disabled }">
    <div
      class="relative w-full h-[40px] tabs-bg"
      :class="[props.mode]"
      :style="{ backgroundColor: props.bgColor }"
    >
      <!-- Tab buttons -->
      <div class="relative w-full h-full flex items-center z-10 p-[4px]" ref="tabsWrapper">
        <div
          v-for="(tab, index) in props.tabs"
          :key="index"
          class="flex items-center"
          ref="tabButtons"
          :class="[props.equalWidth ? 'flex-1' : '']"
        >
          <slot name="btns" :tab="tab" :index="index" :key="tab.value ?? index">
            <button
              class="flex-1 text-[13px] text-center transition-all px-[16px] duration-200 text-#6E6E73 h-[32px] p-[6px] overflow-hidden whitespace-nowrap text-ellipsis"
              :class="[
                activeTab === tab.value ? 'font-medium text-#1C1C1E' : 'font-normal',
                props.disabledTabs.includes(tab.value) ? 'disabled-tab' : '',
                btnClass,
              ]"
              @click="changeTab(tab.value, index)"
            >
              <slot name="tab" :tab="tab" :index="index">
                {{ tab.label }}
              </slot>
            </button>
            <div
              class="line flex-0-auto w-[1px] h-[20px] bg-#E5E5EA"
              v-if="props.showDivider && index + 1 !== props.tabs.length"
            ></div>
          </slot>
        </div>
      </div>

      <!-- Sliding indicator -->
      <div
        class="absolute h-[32px] bg-white shadow-box mask-box"
        :class="animation ? 'transition-all duration-200 ease-in-out' : ''"
        :style="{
          top: '50%',
          width: `${slideTargetWidth}px`,
          transform: `translateY(-50%) translateX(${translateX}px)`,
        }"
      />
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(["change"]);
const tabButtons = ref([]);
const tabsWrapper = ref(null);
const activeTab = ref("tab1");
const activeTabIndex = ref(0);
const slideTargetWidth = ref(0);
const translateX = ref(0);

const props = defineProps({
  animation: {
    type: Boolean,
    default: true,
  },
  tabs: {
    type: Array,
    default: () => [
      { label: "Tab 1", value: "tab1" },
      { label: "Tab 2", value: "tab2" },
      { label: "Tab 3", value: "tab3" },
    ],
  },
  equalWidth: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  bgColor: {
    type: String,
    default: "#E5E5EA",
  },
  defaultTab: {
    type: String,
    default: null,
  },
  disabledTabs: {
    type: Array,
    default: () => [],
  },
  mode: {
    type: String,
    default: "large",
  },
  // 显示分割线
  showDivider: {
    type: Boolean,
    default: false,
  },
  btnClass: {
    type: String,
    default: "",
  },
});

const changeTab = (value, index) => {
  if (props.disabledTabs.includes(value)) {
    return;
  }
  activeTab.value = value;
  activeTabIndex.value = index;
  emit("change", value, index);
  updateSlidePosition();
};

// 更新 translateX 的函数
const updateSlidePosition = () => {
  const currentTab = tabButtons.value[activeTabIndex.value];
  if (currentTab) {
    const { left } = currentTab.getBoundingClientRect();
    const width = currentTab.offsetWidth;
    const { left: wrapperLeft } = tabsWrapper.value.getBoundingClientRect();
    const _left = (wrapperLeft - left).toFixed(2);
    translateX.value = -_left;
    slideTargetWidth.value = props.showDivider ? width - 1 : width;
  }
};
const observer = new ResizeObserver(updateSlidePosition);
onMounted(async () => {
  nextTick(() => {
    activeTab.value = props.defaultTab || props.tabs[0].value;
    activeTabIndex.value = props.defaultTab
      ? props.tabs.findIndex((tab) => tab.value === props.defaultTab)
      : 0;
    tabButtons.value.forEach((button) => observer.observe(button));
    updateSlidePosition();
  });
});

const reset = () => {
  activeTab.value = props.tabs[0].value;
  activeTabIndex.value = 0;
  updateSlidePosition();
};

onUnmounted(() => {
  observer.disconnect();
});

watch(
  () => props.defaultTab,
  (val) => {
    nextTick(() => {
      if (val) {
        activeTab.value = props.defaultTab || props.tabs[0].value;
        activeTabIndex.value = props.defaultTab
          ? props.tabs.findIndex((tab) => tab.value === props.defaultTab)
          : 0;
        updateSlidePosition();
      }
    });
  },
  { deep: true }
);

defineExpose({
  reset,
  updateSlidePosition,
});
</script>

<style scoped lang="less">
.tabs-bg {
  background-color: rgba(28, 28, 30, 0.06);
}
.disabled-tab {
  cursor: not-allowed;
  opacity: 0.6;
}
.large {
  border-radius: 20px;
  .mask-box {
    border-radius: 16px;
  }
}

.middle {
  border-radius: 8px;
  .mask-box {
    border-radius: 6px;
  }
}

.right-line:not(:last-child) {
  &::after {
    content: "";
    position: absolute;
    right: 0px;
    top: 6px;
    width: 1px;
    height: 20px;
    background-color: #e5e5ea;
  }
}
</style>
