import { ref, createApp, TransitionGroup, h } from "vue";
import NotifyConstructor from "../PureNotify.vue";

// 定义通知类型
const NotifyType = {
  SUCCESS: "success",
  ERROR: "error",
  INFO: "info",
  WARNING: "warning",
};

// 定义布局类型
const LayoutType = {
  HORIZONTAL: "horizontal",
  VERTICAL: "vertical",
};

// 默认配置
const defaultOptions = {
  duration: 3000,
  type: NotifyType.INFO,
  toastLayout: LayoutType.HORIZONTAL,
  showButtons: false,
  pureMode: false,
  alwaysShow: false,
  grouping: true, // 默认开启分组 相同类型和消息的通知会合并显示
};

class NotificationManager {
  constructor() {
    this.notifications = ref([]);
    this.seed = 1;
    this.container = null;
    this.app = null;
    this.maxWidth = ref(350);
  }

  // 创建通知容器
  createContainer() {
    if (this.container) return;

    this.app = createApp({
      render: () => {
        // 获取所有通知的宽度，包括新设置的宽度
        const widths = this.notifications.value.map((n) => {
          const width = n.props?.width ?? this.maxWidth.value;
          return typeof width === "number" ? width : parseInt(width?.replace("px", ""));
        });

        // 更新最大宽度（如果没有通知，保持上一次的最大宽度）
        if (widths.length > 0) {
          this.maxWidth.value = Math.max(...widths);
        }

        const widthStyle = `width: ${this.maxWidth.value}px;`;

        return h(
          TransitionGroup,
          {
            name: "dynamic-fade",
            tag: "div",
            appear: true,
            class: ["pure-notify", "pure-notify--vertical"],
            style: widthStyle,
          },
          {
            default: () =>
              this.notifications.value.map((notification) =>
                h(NotifyConstructor, {
                  key: notification.id,
                  ...notification.props,
                  style: widthStyle,
                })
              ),
          }
        );
      },
    });

    this.container = document.createElement("div");
    this.app.mount(this.container);
    document.body.appendChild(this.container);
  }

  // 查找相同类型和消息的通知
  findSimilarNotification(options) {
    if (!options.grouping) return null;

    return this.notifications.value.find(
      (notification) =>
        notification.props.message === options.message && notification.props.type === options.type
    );
  }

  // 创建通知
  create(options) {
    // 规范化参数
    const normalizedOptions = this.normalizeOptions(options);

    this.createContainer();

    // 检查是否有相同类型和消息的通知
    const existingNotification = this.findSimilarNotification(normalizedOptions);

    // 如果找到相同的通知并启用了分组功能，则不创建新通知
    if (existingNotification && normalizedOptions.grouping) {
      // 可以选择重置现有通知的计时器
      if (normalizedOptions.duration > 0) {
        // 返回现有通知的控制对象
        return {
          close: () => this.close(existingNotification.id),
        };
      }
    }

    const id = `notification_${this.seed++}`;
    const notification = {
      id,
      props: {
        id,
        ...normalizedOptions,
        onClose: () => this.close(id),
      },
    };

    this.notifications.value.push(notification);

    return {
      close: () => this.close(id),
    };
  }

  // 规范化配置项
  normalizeOptions(options) {
    if (typeof options === "string") {
      options = { message: options };
    }

    return {
      ...defaultOptions,
      ...options,
    };
  }

  // 关闭通知
  close(id) {
    const index = this.notifications.value.findIndex((item) => item.id === id);
    if (index !== -1) {
      this.notifications.value.splice(index, 1);
    }
  }

  // 清除所有通知
  clearAll() {
    this.notifications.value = [];
    this.maxWidth.value = 350;
  }

  // 销毁实例
  destroy() {
    if (this.app) {
      this.app.unmount();
      this.container?.remove();
      this.container = null;
      this.app = null;
    }
  }
}

// 创建单例
const notificationManager = new NotificationManager();

// 创建通知函数
const notify = (options) => notificationManager.create(options);

// 添加类型方法
notify.success = (options) =>
  notificationManager.create({
    ...(typeof options === "string" ? { message: options } : options),
    type: NotifyType.SUCCESS,
  });

notify.error = (options) =>
  notificationManager.create({
    ...(typeof options === "string" ? { message: options } : options),
    type: NotifyType.ERROR,
  });

notify.info = (options) =>
  notificationManager.create({
    ...(typeof options === "string" ? { message: options } : options),
    type: NotifyType.INFO,
  });
notify.warning = (options) =>
  notificationManager.create({
    ...(typeof options === "string" ? { message: options } : options),
    type: NotifyType.WARNING,
  });

notify.clearAll = () => notificationManager.clearAll();

// 导出类型定义
export { NotifyType, LayoutType };
export default notify;
