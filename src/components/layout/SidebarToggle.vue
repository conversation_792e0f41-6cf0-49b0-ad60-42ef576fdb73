<script setup>
import { computed } from "vue";
import { useAppStore } from "@/stores/app.js";
import ToggleIcon from "@/assets/icons/toggle-sidebar-icon.svg";
import { menuItems as routeMenuItems } from "@/config/menu.js";
import ToggleMobileMenuIcon from "@/assets/icons/menu/mobile/menu.svg";

const route = useRoute();

const appStore = useAppStore();

// 从 store 中获取状态
const shouldShowSidebar = computed(() => appStore.shouldShowSidebar);
const isMobile = computed(() => appStore.isMobile);
const hasChildren = ref(false);

watch(
  () => route.path,
  (newPath) => {
    // 如果当前路由的父路由有子菜单，则显示展开按钮
    hasChildren.value = routeMenuItems.find((item) => {
      if (item.path === newPath) {
        return item.children && item.children.length > 0;
      } else {
        return item.children?.some((child) => child.path === newPath);
      }
    });
    // 如果是移动端,切换路由的时候关闭二级路由导航
    if (isMobile.value) {
      appStore.closeMobileMenu();
    }
  },
  { immediate: true }
);

// 切换侧边栏展开状态
function toggleSidebar() {
  appStore.toggleExpanded();
}
</script>

<template>
  <ToggleIcon
    v-if="!isMobile"
    :class="[
      'w-[40px] h-[40px] fixed top-[8px] p-0 flex items-center justify-center cursor-pointer transition-transform duration-300 z-[40]',
      // 桌面端：根据侧边栏状态调整位置
      shouldShowSidebar ? 'translate-x-[219px]' : 'translate-x-[16px]',
    ]"
    @click="toggleSidebar"
  />
  <ToggleMobileMenuIcon
    v-if="isMobile && hasChildren"
    class="w-[40px] h-[40px] fixed top-[8px] left-[16px] p-0 cursor-pointer z-[40]"
    @click="toggleSidebar"
  ></ToggleMobileMenuIcon>
</template>

<style scoped></style>
