<script setup lang="ts">
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useAppStore } from "@/stores/app.js";
import { useUserStore } from "@/stores/user.js";
import type { MenuItem } from "@dmp/components";
import { menuItems as routeMenuItems } from "@/config/menu.js";
import ToggleSidebarCloseIcon from "@/assets/icons/toggle-sidebar-close.svg";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const route = useRoute();
const router = useRouter();
const appStore = useAppStore();
const userStore = useUserStore();

// 从 store 中获取状态
const shouldShowSidebar = computed(() => appStore.shouldShowSidebar);
const isMobile = computed(() => appStore.isMobile);

// 菜单项配置 - 从路由文件导入并根据权限过滤
const filteredMenus = userStore.userRtm ? userStore.filterMenuItems(routeMenuItems) : [];

const filteredMenusTransfor = computed(() => {
  return filteredMenus.map((item) => ({
    ...item,
    title: t(item.title),
    children: item.children?.map((child) => ({
      ...child,
      title: t(item.title),
    })),
  }));
});

const mobileSecondaryMenu = computed(() => {
  return (
    filteredMenusTransfor.value.find(
      (item) =>
        item.path === route.path || item.children?.some((child) => child.path === route.path)
    )?.children || []
  );
});

function gotoPage(menuItem: MenuItem) {
  if (menuItem.path && menuItem.path !== route.path) {
    router.push(menuItem.path);
    // 移动端点击子菜单后关闭菜单
    if (isMobile.value) {
      appStore.closeMobileMenu();
    }
  }
}

// 切换侧边栏展开状态
function toggleSidebar() {
  appStore.toggleExpanded();
}
</script>

<template>
  <dmp-sidebar-mobile
    v-if="isMobile"
    :initPath="route.path"
    :menuItems="mobileSecondaryMenu"
    :show="shouldShowSidebar"
    @select="gotoPage"
  >
    <template #toogleIcon>
      <ToggleSidebarCloseIcon class="w-[40px] h-[40px] cursor-pointer" @click="toggleSidebar" />
    </template>
  </dmp-sidebar-mobile>
  <dmp-sidebar
    v-else
    :initPath="route.path"
    :menuItems="filteredMenusTransfor"
    :show="shouldShowSidebar"
    :isMobile="isMobile"
    @select="gotoPage"
  />
</template>
