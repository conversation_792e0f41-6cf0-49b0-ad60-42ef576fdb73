@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    background: rgba(255, 255, 255, 0.48);
    border-radius: 16px;
    padding: 12px 16px;
  }

  .glass-card {
    position: relative;
    padding: 20px 16px 16px 16px;
    border-radius: 16px;
    backdrop-filter: blur(24px) saturate(140%);
    -webkit-backdrop-filter: blur(24px) saturate(140%);

    /* 主体背景 */
    background: rgba(255, 255, 255, 0.14);
    /* linear-gradient(135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 100%
    ),
    rgba(255, 255, 255, 0.08); */

    /* 阴影系统 */
    box-shadow:
      -1px -1px 2px 0px rgba(255, 255, 255, 0.32) inset,
      1px 1px 2px 0px rgba(255, 255, 255, 0.32) inset,
      1px 1px 4px 0px rgba(0, 0, 0, 0.08) inset;

    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    will-change: transform;
    transform: translateZ(0);
  }

  .glass-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 16px;
    padding: 1px; /* 边框厚度 */

    /* 渐变边框 - 左上角和右下角偏白 */
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.6) 0%,
      /* 左上角偏白 */ rgba(255, 255, 255, 0.1) 25%,
      /* 过渡 */ rgba(255, 255, 255, 0.4) 50%,
      /* 中间需要一点透明 */ rgba(255, 255, 255, 0.1) 75%,
      /* 过渡 */ rgba(255, 255, 255, 0.5) 100% /* 右下角偏白 */
    );

    /* 使用mask来创建边框效果 */
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: exclude;

    z-index: -1;
  }

  .glass-card::after {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: 25px;

    /* 倾斜的渐变边框 - 模拟光线折射 */
    background: conic-gradient(
      from 45deg at 50% 50%,
      rgba(255, 255, 255, 0.8) 0deg,
      /* 左上角最亮 */ rgba(255, 255, 255, 0.2) 45deg,
      /* 渐变过渡 */ rgba(255, 255, 255, 0.05) 90deg,
      /* 右上角暗 */ rgba(255, 255, 255, 0.1) 135deg,
      /* 右侧过渡 */ rgba(255, 255, 255, 0.6) 180deg,
      /* 右下角亮 */ rgba(255, 255, 255, 0.2) 225deg,
      /* 下方过渡 */ rgba(255, 255, 255, 0.05) 270deg,
      /* 左下角暗 */ rgba(255, 255, 255, 0.3) 315deg,
      /* 左侧过渡 */ rgba(255, 255, 255, 0.8) 360deg /* 回到左上角 */
    );

    /* 创建边框效果 */
    -webkit-mask: radial-gradient(
      circle at center,
      transparent 0,
      transparent calc(100% - 1px),
      black calc(100% - 1px)
    );
    mask: radial-gradient(
      circle at center,
      transparent 0,
      transparent calc(100% - 1px),
      black calc(100% - 1px)
    );

    z-index: -2;
  }

  /* .glass-card:hover {
    transform: translateY(-2px) translateZ(0);
    box-shadow: 
      0 12px 48px rgba(0, 0, 0, 0.08),
      0 4px 16px rgba(0, 0, 0, 0.06);
  } */

  .glass-card:hover::before {
    /* background: linear-gradient(135deg, 
      rgba(255, 255, 255, 0.8) 0%,
      rgba(255, 255, 255, 0.15) 25%,
      rgba(255, 255, 255, 0.08) 50%,
      rgba(255, 255, 255, 0.15) 75%,
      rgba(255, 255, 255, 0.7) 100%
    ); */
  }
}
