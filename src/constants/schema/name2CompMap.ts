/*
 * @Date: 2025-07-16 17:30:17
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-21 14:45:25
 * @FilePath: /MyBusinessV2/src/constants/schema/name2CompMap.ts
 */
import {
  DmpFlexColsCard1x1x1,
  DmpDataCenterCard2x1,
  DmpDataCenterCard1x1,
  DmpRankCard,
  DmpSystemHealthIndicator,
  DmpTipCarousel,
  Dmp<PERSON>lertStatusList,
  DmpHealthDiagnosis,
} from "@dmp/components";
import SalesPerformanceDialog from "../../views/subViews/SalesPerformanceDialog.vue";
import InventoryPerformanceDialog from "../../views/subViews/InventoryPerformanceDialog.vue";
import ProductRankDialog from "@/views/subViews/ProductRankDialog.vue";
import TierStoreRankDialog from "@/views/subViews/TierStoreRankDialog.vue";
import LevelStoreRankDialog from "@/views/subViews/LevelStoreRankDialog.vue";
import MovementStoreRankDialog from "@/views/subViews/MovementStoreRankDialog.vue";
import InventoryWarningDialog from "@/views/subViews/InventoryWarningDialog/InventoryWarningDialog.vue";
import InventoryRemainder from "@/views/subViews/InventoryRemainder/InventoryRemainder.vue";
import SalesWarning from "@/views/subViews/SalesWarning/SalesWarning.vue";

import HealthDiagnosisDialog from "@/views/subViews/HealthDiagnosisDialog2.vue";
import TradeInDialog from "@/views/subViews/TradeInDialog/TradeInDialog.vue";

export const name2CompMap = {
  // Dashboard cards
  DmpFlexColsCard1x1x1,
  DmpDataCenterCard2x1,
  DmpDataCenterCard1x1,
  DmpRankCard,
  DmpSystemHealthIndicator,
  DmpTipCarousel,
  DmpAlertStatusList,
  DmpHealthDiagnosis,

  // Popovers
  SalesPerformanceDialog,
  InventoryPerformanceDialog,
  ProductRankDialog,
  TierStoreRankDialog,
  LevelStoreRankDialog,
  MovementStoreRankDialog,
  InventoryWarningDialog,
  HealthDiagnosisDialog,
  InventoryRemainder,
  SalesWarning,
  TradeInDialog,
};
