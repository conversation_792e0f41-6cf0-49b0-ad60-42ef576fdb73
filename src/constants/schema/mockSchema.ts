export default function getMockData(t) {
  return {
    // 布局
    layout: {
      desktop: [
        {
          id: "operationCenter",
          type: "DmpGlassCard",
          title: t("运营中心"),
          children: [
            {
              type: "DmpRankCard",
              id: "productRank",
              title: t("产品热销排行"),
              api: "/hk_tw/product_rank/show",
            },
            {
              type: "DmpRankCard",
              id: "storeRank",
              title: t("门店评分排行"),
              api: "/hk_tw/store_rank/show",
            },
          ],
        },
        {
          id: "dataCenter",
          type: "DmpGlassCard",
          title: t("数据中心"),
          children: [
            {
              type: "DmpDataCenterCard2x1",
              id: "salesPerformance",
              title: t("销售表现"),
              api: "/hk_tw/datacenter/sales_performance",
            },
            {
              type: "DmpDataCenterCard1x1",
              id: "stockPerformance",
              title: t("库存表现"),
              api: "/hk_tw/datacenter/inventory/summary",
            },
            {
              type: "DmpDataCenterCard1x1",
              id: "retailPerformance",
              title: t("零售项目"),
              api: "/hk_tw/datacenter/retail/summary",
            },
          ],
        },

        {
          id: "healthDiagnosis",
          type: "DmpGlassCard",
          title: t("健康诊断"),
          children: [
            {
              type: "DmpHealthDiagnosis",
              id: "healthDiagnosis",
              title: t("健康诊断"),
              api: "/hk_tw/health/home_page",
            },
          ],
        },
      ],
      mobile: [
        //... 移动端另一套布局,暂时不需要
      ],
    },
    popovers: {
      "salesPerformance.*": {
        type: "SalesPerformanceDialog",
        title: `$1 ${t("销售表现")}`,
        props: {
          lob: "$1",
        },
      },
      "stockPerformance.*": {
        type: "InventoryPerformanceDialog",
        title: `$1 ${t("库存表现")}`,
        props: {
          lob: "$1",
        },
      },
      "retailPerformance.*": {
        type: "InventoryPerformanceDialog",
        title: `$1 ${t("库存表现")}`,
        props: {
          lob: "$1",
        },
      },
      "productRank.*": {
        type: "ProductRankDialog",
        title: `$1 ${t("热销排行")}`,
        props: {
          lob: "$1",
        },
      },
      "storeRank.*": {
        type: "StoreRankDialog",
        title: t("门店评分排名"),
        props: {
          lob: "$1",
        },
      },
      healthDiagnosis: {
        type: "HealthDiagnosisDialog",
        title: t("健康诊断"),
      },
      inv_warning: {
        type: "InventoryRemainder",
        title: t("库存预警"),
      },
      inv_remind: {
        type: "InventoryWarningDialog",
        title: t("库存提醒"),
      },
      so_warning: {
        type: "SalesWarning",
        title: t("销售预警"),
      },
    },
  };
}
