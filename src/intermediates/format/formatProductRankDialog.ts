import { ProductRankDialogResponse } from "@/api/interface/ProductRankDialog";
import type { PopoverRankItem } from "@dmp/components";

export const formatProductRank: (
  data: ProductRankDialogResponse
) => Record<string, PopoverRankItem[]> = (data = {}) => {
  const result: Record<string, PopoverRankItem[]> = {};
  if (Object.keys(data).length === 0) return result;
  Object.entries(data).forEach(([key, value]) => {
    result[key] = value.map((item) => ({
      rank: item.rank,
      name: item.sub_lob,
      value: item.market_share,
      sku: item?.sku ?? "",
      desc: item?.sku ?? "",
    }));
  });
  return result;
};
