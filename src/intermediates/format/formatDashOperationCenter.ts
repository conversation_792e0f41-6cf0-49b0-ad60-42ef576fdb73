// 产品排名接口
import { useUserStore } from "@/stores/user.js";
import { formatNumber } from "@/utils/number";
import { i18n } from "@/i18n";

const { t } = i18n.global;

export interface ProductRankItem {
  rank: number;
  sub_lob: string;
  storsize_long_desc: string;
  color_long_desc: string;
  [key: string]: any; // 其它字段
}

export interface StoreRankItem {
  rank: number;
  metric: string;
  [key: string]: any; // 其它字段
}

export interface ProductRankData {
  lob: string;
  data: ProductRankItem[];
}

export interface StoreRankData {
  metric: string;
  metric_name?: string;
  ranks: StoreRankItem[];
}

export interface ProductRankResponse {
  code: number;
  data: ProductRankData[];
}

export interface StoreRankResponse {
  code: number;
  data: StoreRankData[];
}

// 默认Tab配置接口
export interface DefaultTab {
  label: string;
  value: string;
}

// format数据
export const formatProductRankData = (data: ProductRankData[], defaultTabs: DefaultTab[] = []) => {
  if (data && data.length > 0) {
    const tabs = defaultTabs;
    const rankData = data
      .flatMap((item) =>
        (item.data || []).map((dataItem) => ({
          ...dataItem,
          lob: item.lob,
          name: item.lob,
          id: `${item.lob}-${dataItem.rank}`,
          title: dataItem.sub_lob,
          subTitle: dataItem?.sku_short_description ?? "",
        }))
      )
      .sort((a, b) => a.rank - b.rank);

    return {
      tabs,
      rankData,
    };
  }

  // 如果接口数据为空或无效，使用默认配置
  return {
    tabs: defaultTabs,
    rankData: [],
    hideImage: false,
  };
};
// 获取子标题
const getSubTitle = (item: StoreRankItem, userRtm: string) => {
  switch (userRtm) {
    case "TW Carrier":
      return `${t("本周销量")} ${formatNumber(item?.pos_lob_so_cw ?? "-")} · ${t("季度累计销量")} ${formatNumber(item?.pos_lob_so_qtw ?? "-")}`;
    case "HK Carrier":
      return `${item?.pos_lob_so_cwp1_drk_tier ?? "-"}`;
    default:
      return `${item?.level || "-"} · ${item?.score ?? "-"} ${t("分")}`;
  }
};

// format 门店评分
export const formatStoreRankData = (data: StoreRankData[], defaultTabs: DefaultTab[] = []) => {
  const userStore = useUserStore();
  const userRtm = userStore.userRtm;
  const tabs = defaultTabs.map((item) => ({
    label: t(item.label),
    value: item.value,
  }));

  if (data && data.length > 0) {
    const rankData = data
      .flatMap((item) =>
        (item.ranks || []).map((dataItem) => {
          const index = tabs.findIndex((tab) => tab.value === item.metric_name);
          if (index > -1) {
            tabs[index].value = item.metric;
          }
          return {
            ...dataItem,
            lob: item.metric,
            name: item.metric_name,
            id: `${item.metric}-${dataItem.rank}`,
            title: dataItem.pos_name,
            subTitle: getSubTitle(dataItem, userRtm),
          };
        })
      )
      .sort((a, b) => a.rank - b.rank);

    return {
      tabs,
      rankData,
      hideImage: true, // 门店评分不显示图片
    };
  }

  // 如果接口数据为空或无效，使用默认配置
  return {
    tabs,
    rankData: [],
  };
};
