import { StoreRankDialogResponse } from "@/api/interface/StoreRankDialog";
import type { PopoverRankItem } from "@dmp/components";
import { formatNumber } from "@/utils/number";
import { i18n } from "@/i18n";

const { t } = i18n.global;

export const formatTierStoreRank: (data: StoreRankDialogResponse) => PopoverRankItem[] = (
  data = []
) =>
  data.map((item) => ({
    rank: item.rank,
    name: item.pos_name,
  }));

export const formatMovementStoreRank: (data: StoreRankDialogResponse) => PopoverRankItem[] = (
  data = []
) =>
  data.map((item) => ({
    rank: item.rank,
    name: item.pos_name,
    value: item.hq_pos_lob_so_qtw_qtlw_drk_diff,
    desc: `${t("本周销量")} ${formatNumber(item.pos_lob_so_cw)} · ${t("季度累计销量")} ${formatNumber(item.pos_lob_so_qtw)}`,
    diffType: item.hq_pos_lob_so_qtw_qtlw_drk_diff_type,
  }));

export const formatLevelStoreRank: (data: StoreRankDialogResponse) => PopoverRankItem[] = (
  data = []
) =>
  data.map((item) => ({
    rank: item.rank,
    name: item.pos_name,
    value: item.score,
  }));
