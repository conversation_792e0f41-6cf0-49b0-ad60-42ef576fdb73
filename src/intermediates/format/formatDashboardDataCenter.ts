import iPhoneIconUrl from "@/assets/icons/lob/iPhone.svg?url";
import iPadIconUrl from "@/assets/icons/lob/iPad.svg?url";
import MacIconUrl from "@/assets/icons/lob/Mac.svg?url";
import WatchIconUrl from "@/assets/icons/lob/Watch.svg?url";
import AirPodsIconUrl from "@/assets/icons/lob/AirPods.svg?url";
import Installment from "@/assets/icons/retail/installment.svg?url";
import NewMachineSetting from "@/assets/icons/retail/new-machine-settings.svg?url";
import PersonalTraining from "@/assets/icons/retail/personal-training.svg?url";
import TradeIn from "@/assets/icons/retail/trade-in.svg?url";
import ACPP from "@/assets/icons/retail/acpp.svg?url";
import DataTransfer from "@/assets/icons/retail/data-transfer.svg?url";
import { useConfigStore } from "@/stores/config.js";
import {
  SalesPerformanceResponse,
  InventorySummaryResponse,
  RetailSummaryResponse,
} from "@/api/interface/DashboardDataCenter";
import { formatNumberWithUnit } from "@/utils/number";
import { i18n } from "@/i18n";

const { t } = i18n.global;

const lobIcons = {
  iPhone: iPhoneIconUrl,
  iPad: iPadIconUrl,
  Mac: MacIconUrl,
  Watch: WatchIconUrl,
  AirPods: AirPodsIconUrl,
};

const retailIcons = {
  Financing: Installment,
  Setup: NewMachineSetting,
  MyCoach: PersonalTraining,
  "Trade-in": TradeIn,
  ACPP: ACPP,
  "Data Transfer": DataTransfer,
};

const retailUnit = {
  Financing: "单",
  Setup: "个",
  MyCoach: "节",
  "Trade-in": "个",
  ACPP: "片",
  "Data Transfer": "次",
};

// 销售表现
export const formatSalesPerformanceData = (data: SalesPerformanceResponse) => {
  const configStore = useConfigStore();
  const tabs = configStore.getSalesPerformanceDefaultTabs;

  return tabs.map((tab) => {
    const item = data?.find((d) => d?.lob === tab.value);
    const weekly =
      item?.weekly != null ? formatNumberWithUnit(item.weekly, "台") : { num: "-", unit: "台" };
    const quarter =
      item?.quarter != null ? formatNumberWithUnit(item.quarter, "台") : { num: "-", unit: "台" };

    return {
      name: tab.label,
      countUnitList: [
        {
          count: weekly.num,
          unit: weekly.unit,
          extra: tabs.length === 1 ? `${tab.label} ${t("本周")}` : t("本周"),
        },
        {
          count: quarter.num,
          unit: quarter.unit,
          extra: tabs.length === 1 ? `${tab.label} ${t("季度")}` : t("季度"),
        },
      ],
      icon: lobIcons?.[tab.value] ?? "",
    };
  });
};

// 库存表现
export const formatInventorySummaryData = (data: InventorySummaryResponse) => {
  const configStore = useConfigStore();
  const tabs = configStore.getInventoryPerformanceDefaultTabs;
  return tabs.map((tab) => {
    const item = data?.find((d) => d?.lob === tab.value);
    const inv = formatNumberWithUnit(item?.inv ?? 0, "台");
    return {
      name: tab.label,
      count: inv.num,
      unit: inv.unit,
      icon: lobIcons?.[tab.value] ?? "",
    };
  });
};

// 零售项目
export const formatRetailSummaryData = (data: RetailSummaryResponse["list"]) => {
  const configStore = useConfigStore();
  const rawTabs = configStore.getSalesProjectDefaultTabs;
  const tabsFormat = rawTabs.map((tab) => {
    return {
      ...tab,
      value: tab.value,
    };
  });

  const list = tabsFormat.map((tab) => {
    const item = data?.find((d) => d?.name === tab.value);
    const cnt = item?.cnt ?? 0;
    const inv = formatNumberWithUnit(cnt, retailUnit[tab.value]);
    return {
      name: tab?.label ?? "",
      count: inv.num,
      unit: inv.unit,
      icon: retailIcons?.[tab?.label] ?? "",
    };
  });

  return list;
};
