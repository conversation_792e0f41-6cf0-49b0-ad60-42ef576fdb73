import { HealthDiagnosisResponse, HealthMetric } from "@/api/interface/DashboardHealthDiagnosis";
import { formatNumber } from "@/utils/number";
import type { HealthDiagnosisProps, AlertStatusItem } from "@dmp/components";

/**
 * 根据级别获取颜色和渐变
 */
function getLevelColors(level: string): {
  color?: string;
  gradient: string[];
  statusLevel: "normal" | "warning" | "good" | "qualified";
  textColor: string;
} {
  const gradientMap = {
    warning: ["#FF8175", "#F63F54"],
    normal: ["#2EB972", "#9DE76A"],
    good: ["#2EB972", "#9DE76A"],
    qualified: ["#FFE167", "#FF9500"],
  };
  const normalColorLevel = ["good", "normal", "nodata"];

  return {
    color: normalColorLevel.includes(level) ? "rgba(28,28,30,0.26)" : undefined,
    textColor: normalColorLevel.includes(level)
      ? "rgba(28,28,30,0.72)"
      : gradientMap[level as keyof typeof gradientMap][1],
    gradient: gradientMap[level as keyof typeof gradientMap] || ["#2EB972", "#9DE76A"],
    statusLevel: level as "normal" | "warning" | "good" | "qualified",
  };
}

/**
 * 根据分数和级别生成健康提示
 */
function generateHealthTips(level: string): string[] {
  const tips = {
    warning: "您的系統出現多個預警信號，需要重點關注",
    normal: "系統健康度良好，部分模塊需優化",
    good: "您的業務總體健康，個別指標需要關注",
  };
  return [tips?.[level] || "-"];
}

function calculateProgress(
  warningCnt: number | "-",
  maxWarningCnt: number | "-"
): {
  progress: number;
  warningCnt: number | "-";
} {
  if (warningCnt === "-" || maxWarningCnt === "-") {
    return {
      progress: 0,
      warningCnt: "-",
    };
  }
  return {
    progress: Math.max(0, (warningCnt / maxWarningCnt) * 100),
    warningCnt: warningCnt as number,
  };
}

/**
 * 格式化指标数据为警报状态项
 */
function formatMetricsToAlertItems(metrics: HealthMetric[]): AlertStatusItem[] {
  const levelZhMap = {
    warning: "重点关注",
    normal: "基本正常",
    qualified: "需要关注",
    good: "继续保持",
  };
  return metrics.map((metric) => {
    const { color, gradient, statusLevel, textColor } = getLevelColors(metric?.level || "nodata");
    const progress = calculateProgress(metric?.warning_cnt ?? "-", metric?.max_warning_cnt ?? "-");
    const hideUnit =
      metric?.warning_cnt === undefined ||
      metric?.warning_cnt === null ||
      metric?.max_warning_cnt === undefined ||
      metric?.max_warning_cnt === null;

    const statusText = levelZhMap?.[metric?.level as keyof typeof levelZhMap] || "暂无数据";

    return {
      id: metric.name || "",
      title: metric.cn_name || "",
      progress: progress.progress,
      warningCnt: formatNumber(progress.warningCnt),
      color,
      statusText,
      gradient,
      statusLevel,
      showText: true,
      progressUnit: hideUnit ? "" : "次",
      textColor,
    };
  });
}

// 健康诊断数据格式化
export function formatHealthDiagnosisData(
  data: HealthDiagnosisResponse
): Partial<HealthDiagnosisProps> {
  const { summary, metrics } = data;

  // 过滤掉空数据
  const filteredMetrics = metrics.filter((item) => item !== null);

  return {
    progress: summary.score,
    statusText: "健康度",
    healthTips: generateHealthTips(summary.level),
    alertItems: formatMetricsToAlertItems(filteredMetrics),
    level: summary.level,
  };
}
