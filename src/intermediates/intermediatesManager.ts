type Intermediate<T, Y> = {
  fetcher: () => Promise<T>;
  formatter: (data: T) => Y;
};

export default class IntermediatesManager {
  static _instance: IntermediatesManager;

  translatorMap = new Map<string, Intermediate<any, any>>();

  static get instance() {
    if (!this._instance) {
      this._instance = new IntermediatesManager();
    }
    return this._instance;
  }

  getKey(compName: string, apiPath: string): string {
    return `${compName}:${apiPath}`;
  }

  register<T, Y>(
    compName: string,
    apiPath: string,
    fetcher: (apiPath: string) => Promise<T>,
    formatter: (data: T) => Y
  ) {
    this.translatorMap.set(this.getKey(compName, apiPath), {
      fetcher: () => fetcher(apiPath),
      formatter,
    });
  }

  get<T, Y>(compName: string, apiPath: string) {
    return this.translatorMap.get(this.getKey(compName, apiPath)) as Intermediate<T, Y>;
  }
}
