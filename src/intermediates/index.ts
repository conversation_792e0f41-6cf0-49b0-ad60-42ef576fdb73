import IntermediatesManager from "@/intermediates/intermediatesManager.ts";
import { get } from "@/utils/http.ts";
import dayjs from "dayjs";
import {
  SalesPerformanceResponse,
  InventorySummaryResponse,
  RetailSummaryResponse,
} from "@/api/interface/DashboardDataCenter";
import {
  formatSalesPerformanceData,
  formatInventorySummaryData,
  formatRetailSummaryData,
} from "./format/formatDashboardDataCenter";
import { formatProductRankData, formatStoreRankData } from "./format/formatDashOperationCenter";
import { HealthDiagnosisResponse } from "@/api/interface/DashboardHealthDiagnosis";
import { formatHealthDiagnosisData } from "./format/formatDashboardHealthDiagnosis";
import { useConfigStore } from "@/stores/config";
import { ProductRankResponse, StoreRankResponse } from "./format/formatDashOperationCenter";
import { useCycleSelector } from "@/views/subViews/components/useCycleSelector";
import { i18n } from "@/i18n";
import { praseUpdateTime } from "@/utils/util";
import customParseFormat from "dayjs/plugin/customParseFormat";

const intermediatesManager = IntermediatesManager.instance;
const { t } = i18n.global;
dayjs.extend(customParseFormat);

// 销售表现
intermediatesManager.register(
  "DmpDataCenterCard2x1",
  "/hk_tw/datacenter/sales_performance",
  async (apiPath: string) => {
    const { current } = useCycleSelector("salesPerformance");
    const { code = 0, data = [] } = await get<SalesPerformanceResponse>(apiPath, {
      fiscal_week_year: current.value?.value ?? null,
    });
    if (code !== 0)
      return {
        list: [],
        updateTime: "",
      };
    return {
      list: data,
      updateTime: current.value?.label,
    };
  },
  (data: { list: SalesPerformanceResponse; updateTime: string }) => ({
    list: formatSalesPerformanceData(data.list),
    useToolTipUpdateTime: formatSalesPerformanceData(data.list).length <= 2,
    updateTime: praseUpdateTime(data.updateTime, t("数据已更新"), (v) => v.slice(4)),
  })
);

// 库存表现
intermediatesManager.register(
  "DmpDataCenterCard1x1",
  "/hk_tw/datacenter/inventory/summary",
  async (apiPath: string) => {
    const { current } = useCycleSelector("stockPerformance", false);
    const { code = 0, data = [] } = await get<InventorySummaryResponse>(apiPath, {
      fiscal_dt: current.value?.value ?? null,
    });
    if (code !== 0)
      return {
        list: [],
        updateTime: "",
      };
    return {
      list: data,
      updateTime: current.value?.value,
    };
  },
  (data: { list: InventorySummaryResponse; updateTime: string }) => ({
    list: formatInventorySummaryData(data.list),
    useToolTipUpdateTime: formatInventorySummaryData(data.list).length <= 2,
    updateTime: praseUpdateTime(data.updateTime, t("数据已更新"), (v) =>
      dayjs(v, "YYYY-MM-DD").format("MM/DD")
    ),
  })
);

// 零售项目
intermediatesManager.register(
  "DmpDataCenterCard1x1",
  "/hk_tw/datacenter/retail/summary",
  async (apiPath: string) => {
    const { current } = useCycleSelector("retailPerformance");
    const { code = 0, data } = await get<RetailSummaryResponse>(apiPath, {
      fiscal_week_year: current.value?.value ?? null,
    });
    if (code !== 0)
      return {
        list: [],
        updateTime: "",
      };
    return {
      list: data.list,
      updateTime: current.value?.label,
    };
  },
  (data: { list: RetailSummaryResponse["list"]; updateTime: string }) => ({
    list: formatRetailSummaryData(data.list),
    updateTime: praseUpdateTime(data.updateTime, t("数据已更新"), (v) => v.slice(4)),
  })
);

intermediatesManager.register(
  "DmpRankCard",
  "/hk_tw/store_rank/show",
  async (apiPath: string) => {
    const id = "storeRank";
    const { current } = useCycleSelector(id);

    try {
      const { code = 0, data = [] }: StoreRankResponse = await get(apiPath, {
        fiscal_week_year: current.value?.value ?? null,
      });

      // 使用format函数，传入默认配置
      return code === 0
        ? {
            data,
            updateTime: current.value?.label,
          }
        : {
            data: [],
            updateTime: "",
          };
    } catch (error) {
      console.log("门店排行接口请求失败:", error);
      // 异常情况下也使用默认配置
      return {
        data: [],
        updateTime: "",
      };
    }
  },
  (data: { data: StoreRankResponse["data"]; updateTime: string }) => {
    const id = "storeRank";
    const configStore = useConfigStore();
    const defaultTabs = configStore.getStoreRankDefaultTabs;
    const result = formatStoreRankData(data.data, defaultTabs);
    return {
      id,
      ...result,
      updateTime: praseUpdateTime(data.updateTime, t("数据已更新"), (v) => v.slice(4)),
    };
  }
);

intermediatesManager.register(
  "DmpRankCard",
  "/hk_tw/product_rank/show",
  async (apiPath: string) => {
    const id = "productRank";
    const { current } = useCycleSelector(id);
    try {
      const { code = 0, data = [] }: ProductRankResponse = await get(apiPath, {
        fiscal_week_year: current.value?.value ?? null,
      });

      return code === 0
        ? {
            data,
            updateTime: current.value?.label,
          }
        : {
            data: [],
            updateTime: "",
          };
    } catch (error) {
      console.log("产品排行接口请求失败:", error);
      // 异常情况下也使用默认配置
      return {
        data: [],
        updateTime: "",
      };
    }
  },
  (data: { data: ProductRankResponse["data"]; updateTime: string }) => {
    const id = "productRank";
    const configStore = useConfigStore();
    const defaultTabs = configStore.getProductRankDefaultTabs;
    const result = formatProductRankData(data.data, defaultTabs);
    return {
      id,
      ...result,
      updateTime: praseUpdateTime(data.updateTime, t("数据已更新"), (v) => v.slice(4)),
    };
  }
);

// 健康诊断
intermediatesManager.register(
  "DmpHealthDiagnosis",
  "/hk_tw/health/home_page",
  async (apiPath: string) => {
    const { current: currentWeekYear } = useCycleSelector("healthDiagnosis");
    const { current: currentDay } = useCycleSelector("healthDiagnosis", false);
    try {
      const { code = 0, data }: { code?: number; data: HealthDiagnosisResponse } = await get(
        apiPath,
        {
          fiscal_week_year: currentWeekYear.value?.value ?? null,
          fiscal_dt: currentDay.value?.value ?? null,
        }
      );

      if (code === 0 && data) {
        // 格式化 API 数据为组件需要的格式
        return {
          data,
          updateTime: currentDay.value?.value,
        };
      }
    } catch (error) {
      console.error("获取健康诊断数据失败:", error);
    }
  },
  (data: { data: HealthDiagnosisResponse; updateTime: string }) => ({
    ...formatHealthDiagnosisData(data.data),
    updateTime: praseUpdateTime(data.updateTime, t("数据已更新"), (v) =>
      dayjs(v, "YYYY-MM-DD").format("MM/DD")
    ),
  })
);
