import { createI18n } from "vue-i18n";
import { useAppStore } from "@/stores/app.js";

import en from "./en.json";
import zh from "./zh.json";
import hk from "./hk.json";
import tw from "./tw.json";

export const i18n = createI18n({
  locale: "zh",
  fallbackLocale: "zh",
  messages: {
    en,
    zh,
    hk,
    tw,
  },
});

// 加载语言包
const languageModules: Record<string, () => Promise<object>> = import.meta.glob("./*.json");

// 语言加载函数
export async function loadLanguageAsync(lang, source = "auto") {
  // 加载过了 就直接设置
  if (i18n.global.availableLocales.includes(lang)) {
    return setI18nLanguage(lang, source);
  }

  // 动态导入语言包
  try {
    const modulePath = `./${lang}.json`;
    if (languageModules[modulePath]) {
      const messages = (await languageModules[modulePath]()) as { default: object };
      // 注册语言包
      i18n.global.setLocaleMessage(lang, messages.default);
      return setI18nLanguage(lang, source);
    } else {
      throw new Error(`Language file ${lang}.json not found`);
    }
  } catch (e) {
    console.error(`Could not load language ${lang}:`, e);
    return Promise.reject(e);
  }
}

// 设置语言
function setI18nLanguage(lang, source = "auto") {
  // 正确设置语言
  i18n.global.locale = lang;
  console.log("设置语言", lang, "来源:", source);

  // 更新 app store 中的当前使用语言
  const appStore = useAppStore();

  // 设置 html 的 lang 属性
  document.querySelector("html").setAttribute("lang", lang);
  // 保存到 sessionStorage
  sessionStorage.setItem("language", lang);
  sessionStorage.setItem("languageSource", source); // 保存语言设置来源
  // 在store中设置当前使用语言
  appStore.currentUseLang = lang;

  // 设置当前使用语言
  return lang;
}

// 获取浏览器语言
export function getBrowserLanguage() {
  const browserLang = navigator.language.toLowerCase();
  if (browserLang.indexOf("en") > -1) {
    return "en"; // 英文
  } else if (browserLang.indexOf("zh") > -1 && browserLang.indexOf("cn") > -1) {
    return "zh"; // 简体中文
  } else if (browserLang.indexOf("zh") > -1 && browserLang.indexOf("hk") > -1) {
    return "hk"; // 港繁
  } else if (browserLang.indexOf("zh") > -1 && browserLang.indexOf("tw") > -1) {
    return "tw"; // 台繁
  } else {
    return "zh"; // 默认简体中文
  }
}

// 根据rtm设置对应 的 语言
export async function setupLanguageByUserRtm(userRtm: string) {
  let targetLang = "zh"; // 目标语言
  let sourceType = "rtm"; // 默认来源类型

  // 检查是否手动设置过语言了，只有用户手动设置的优先级最高
  const userSetLanguage = sessionStorage.getItem("language");
  const languageSource = sessionStorage.getItem("languageSource");

  if (userSetLanguage && languageSource === "manual") {
    targetLang = userSetLanguage;
    sourceType = "manual"; // 用户手动设置的标记
  } else {
    // 如果用户未手动设置语言，则根据RTM来决定
    const HK = userRtm?.startsWith("HK") || false;
    const TW = userRtm?.startsWith("TW") || false;

    if (HK) {
      targetLang = "hk";
    } else if (TW) {
      targetLang = "tw";
    }
    // 都没有匹配使用 浏览器的语言作为默认语言
    if (targetLang === "zh") {
      targetLang = getBrowserLanguage();
    }
    sourceType = "rtm"; // 根据rtm推断的语言
  }

  try {
    const res = await loadLanguageAsync(targetLang, sourceType);
    return res;
  } catch (error) {
    // 如果设置失败了， 回到中文上
    return await loadLanguageAsync("zh", sourceType);
  }
}
