import { createRouter, createWebHistory } from "vue-router";
import { ROUTE_NAMES } from "@/constants/routes.js";

// 导入配置模块
import { useUserStore } from "@/stores/user.js";

// 重新导出菜单配置供外部使用
export { menuItems } from "@/config/menu.js";
import routes from "@/config/routes.js";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

// 路由守卫
// 系统初始化标记，确保只执行一次1
let systemInitialized = false;

router.beforeEach(async (to, from, next) => {
  try {
    const userStore = useUserStore();

    // 检测 URL 参数并处理
    const resellerId = to.query?.RESELLER_ID;
    const resellerRtm = to.query?.RESELLER_RTM;

    if (resellerId && resellerRtm) {
      // 将参数写入 sessionStorage
      sessionStorage.setItem("reseller-id", resellerId);
      sessionStorage.setItem("reseller-rtm", resellerRtm);

      // 将参数写入 localStorage  触发用户回退校验信息功能1
      localStorage.setItem("reseller-id", resellerId);
      localStorage.setItem("reseller-rtm", resellerRtm);

      // 清除 URL 中的参数，使用 replace 进行跳转
      const cleanQuery = { ...to.query };
      delete cleanQuery.RESELLER_ID;
      delete cleanQuery.RESELLER_RTM;

      next({
        path: to.path,
        query: cleanQuery,
        hash: to.hash,
        replace: true,
      });
      return; // 显式跳转后，终止当前守卫执行
    }

    // 系统初始化 - 只在第一次访问时执行
    if (!systemInitialized) {
      console.log("🚀 系统开始初始化...");

      // 传入router实例，让store处理跳转
      userStore
        .initializeApp(router)
        .then((result) => {
          if (result.success) {
            console.log("✅ 系统初始化完成，用户有权限");
          } else {
            console.log("❌ 系统初始化失败，用户无权限1");
          }
        })
        .catch((error) => {
          console.error("❌ 系统初始化过程中发生错误:", error);
        });

      systemInitialized = true;
      console.log("🎯 系统初始化标记已设置");
    }

    // 如果正在初始化，跳转到认证页面（记录原始目标）
    if (userStore.isInitializing) {
      if (to.name !== ROUTE_NAMES.AUTHENTICATING) {
        console.log("🔄 系统初始化中，跳转到认证页面");
        // 将原始目标路由作为查询参数传递
        next({
          name: ROUTE_NAMES.AUTHENTICATING,
          query: {
            redirect: to.fullPath,
          },
        });
        return;
      }
    }
    // 如果用户有权限但访问特殊页面，进行相应跳转
    else if (userStore.hasAppPermission === true) {
      if (to.name === ROUTE_NAMES.AUTHENTICATING) {
        // 如果用户有权限但访问认证页面，检查是否有redirect参数
        const redirectPath = to.query?.redirect;
        if (
          redirectPath &&
          redirectPath !== "/authenticating" &&
          redirectPath !== "/no-permission"
        ) {
          console.log("✅ 用户有权限，跳转到目标页面:", redirectPath);
          next(redirectPath);
          return;
        } else {
          console.log("✅ 用户有权限，跳转到首页");
          next({ name: ROUTE_NAMES.HOME });
          return;
        }
      } else if (to.name === ROUTE_NAMES.NO_PERMISSION) {
        console.log("✅ 用户有权限，不允许访问无权限页面，跳转到首页");
        next({ name: ROUTE_NAMES.HOME });
        return;
      }

      // ==================== RTM权限检查 ====================
      // 检查RTM权限（只有在用户有基本应用权限时才检查RTM权限）
      if (userStore.userRtm && to.meta) {
        const hasRtmPermission = userStore.checkRoutePermission(to, routes);

        if (!hasRtmPermission) {
          console.log("🚫 用户RTM权限不足，无法访问此页面:", to.path);
          console.log("用户RTM:", userStore.userRtm);
          console.log("页面权限要求:", to.meta.permissions);

          // 跳转到无权限页面，并记录原始目标
          next({
            name: ROUTE_NAMES.NO_PERMISSION,
            query: {
              redirect: to.fullPath,
              reason: "insufficient-rtm-permission",
            },
          });
          return;
        }
      }
    }
    // 如果用户无权限但访问需要权限的页面，重定向到无权限页面
    else if (userStore.hasAppPermission === false) {
      if (to.meta?.requiresAuth !== false && to.name !== ROUTE_NAMES.NO_PERMISSION) {
        console.log("🚫 用户无权限，重定向到无权限页面");
        next({ name: ROUTE_NAMES.NO_PERMISSION });
        return;
      }
    }

    // 其他情况正常放行
    console.log("✅ 路由守卫通过，正常跳转");
    next();
  } catch (error) {
    console.error("❌ 路由守卫执行出错:", error);
    // 出错时正常放行，避免路由卡死
    next();
  }
});

router.afterEach((to, from) => {
  console.log(`从 ${from.path} 导航到 ${to.path}`);
});

// ==================== 导出 ====================
export default router;
