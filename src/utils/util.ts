/*
 * @Date: 2025-07-22 15:28:28
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-23 19:13:28
 * @FilePath: /MyBusinessV2/src/utils/util.ts
 */
export const camelCaseToLine = (camelCase) => {
  return camelCase.replace(/\B([A-Z])/g, "_$1").toLowerCase();
};

export const isEmptyObj = (obj) => {
  return JSON.stringify(obj) === "{}";
};

export function toString(params) {
  return typeof params === "string" ? params : JSON.stringify(params);
}

const DEBUG = true;

export function debugLog(...args: any[]) {
  if (DEBUG) {
    console.log.apply(console, args);
  }
}

export function debugError(...args: any[]) {
  if (DEBUG) {
    console.error.apply(console, args);
  }
}

export function parseFiscalCode(code: string): string | null {
  // 匹配 FY后两位数字，Q后1位数字，W后1-2位数字
  const match = code.match(/^FY(\d{2})Q(\d)W(\d{1,2})$/i);
  if (!match) return null;
  const [, year, quarter, week] = match;
  return `20${year}${quarter}${week}`;
}

export function roundToOneDecimalStr(num: number, decimalPlaces = 1): string {
  return (Math.round(num * 10) / 10).toFixed(decimalPlaces);
}

export function fiscal2Number(code: string): number | null {
  const match = code.match(/^(?:FY(\d{2}))?(?:Q(\d))?W(\d{1,2})$/i);
  if (!match) return null;
  const [, year = "00", quarter = "0", week] = match;
  return +`${year}${quarter}${week.length === 1 ? "0" + week : week}`;
}

export const calcRollingList = <T extends any[]>(
  origin: T,
  matchKey: keyof T[0],
  matchValue: any,
  rollingCount: number
): T => {
  const index = origin.findIndex((item) => item?.[matchKey] === matchValue);
  if (index === -1) return [] as T;
  return new Array(rollingCount).fill(0).map((_, i) => origin?.[index - rollingCount + i + 1]) as T;
};

export const praseUpdateTime = (
  time: string,
  suffix: string,
  formatTime?: (time: string) => string
) => {
  return time
    ? `${typeof formatTime === "function" ? formatTime(time) : time}${suffix ? ` ${suffix}` : ""}`
    : "";
};
