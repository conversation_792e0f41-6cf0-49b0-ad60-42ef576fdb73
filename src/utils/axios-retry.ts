import type { AxiosInstance, AxiosRequestConfig, AxiosError } from "axios";

// 重试配置接口
export interface RetryConfig {
  /** 最大重试次数 */
  retries?: number;
  /** 重试延迟基数（毫秒） */
  retryDelay?: number;
  /** 是否启用指数退避 */
  exponentialBackoff?: boolean;
  /** 最大延迟时间（毫秒） */
  maxDelay?: number;
  /** 重试条件函数 */
  retryCondition?: (error: AxiosError) => boolean;
  /** 延迟计算函数 */
  delayCalculation?: (retryNumber: number, baseDelay: number) => number;
  /** 重试时的回调 */
  onRetry?: (retryNumber: number, error: AxiosError, requestConfig: AxiosRequestConfig) => void;
  /** 是否启用详细日志 */
  enableLogs?: boolean;
}

// 扩展 AxiosRequestConfig 类型
declare module "axios" {
  export interface AxiosRequestConfig {
    retryConfig?: RetryConfig;
    _retryCount?: number;
  }
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: Required<RetryConfig> = {
  retries: 3,
  retryDelay: 1000,
  exponentialBackoff: true,
  maxDelay: 30000,
  retryCondition: (error: AxiosError) => {
    // 默认重试条件：网络错误或5xx服务器错误
    return (
      !error.response || // 网络错误
      error.code === "ECONNABORTED" || // 超时
      error.code === "ETIMEDOUT" ||
      (error.response.status >= 500 && error.response.status <= 599) || // 5xx错误
      error.response.status === 408 || // 请求超时
      error.response.status === 429 // 请求频率限制
    );
  },
  delayCalculation: (retryNumber: number, baseDelay: number) => {
    // 指数退避算法：delay = baseDelay * (2 ^ retryNumber) + jitter
    const exponentialDelay = baseDelay * Math.pow(2, retryNumber);
    const jitter = Math.random() * 100; // 添加随机抖动
    return exponentialDelay + jitter;
  },
  onRetry: () => {},
  enableLogs: false,
};

/**
 * 计算重试延迟时间
 */
function calculateDelay(retryNumber: number, config: Required<RetryConfig>): number {
  const delay = config.exponentialBackoff
    ? config.delayCalculation(retryNumber, config.retryDelay)
    : config.retryDelay;

  return Math.min(delay, config.maxDelay);
}

/**
 * 延迟
 */
function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 重试逻辑处理
 */
async function handleRetry(
  error: AxiosError,
  axios: AxiosInstance,
  globalConfig: RetryConfig
): Promise<any> {
  const config = error.config!;
  // 合并配置
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...globalConfig, ...config.retryConfig };

  // 初始化重试计数 默认0
  config._retryCount = config._retryCount ?? 0;

  // 检查是否应该重试 如果重试次数大于最大重试次数 或者 重试条件不满足 则不重试
  if (config._retryCount >= retryConfig.retries || !retryConfig.retryCondition(error)) {
    return Promise.reject(error);
  }

  // 增加重试计数
  config._retryCount++;

  // 计算延迟时间
  const delayTime = calculateDelay(config._retryCount - 1, retryConfig);

  // 日志记录
  if (retryConfig.enableLogs) {
    console.warn(
      `🔄 HTTP重试 [${config._retryCount}/${retryConfig.retries}]: ${config.method?.toUpperCase()} ${config.url}`,
      {
        error: error.message,
        delay: `${delayTime}ms`,
        status: error.response?.status,
      }
    );
  }

  // 执行重试回调
  retryConfig.onRetry(config._retryCount, error, config);

  // 延迟后重试
  await delay(delayTime);

  return axios.request(config);
}

/**
 * 给 axios 实例添加重试功能
 */
export function setupAxiosRetry(axios: AxiosInstance, globalConfig: RetryConfig = {}): void {
  // 在响应拦截器中处理重试逻辑
  axios.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      // 如果请求配置中明确禁用了重试，则直接抛出错误
      if (error.config?.retryConfig?.retries === 0) {
        return Promise.reject(error);
      }

      try {
        return await handleRetry(error, axios, globalConfig);
      } catch (retryError) {
        return Promise.reject(retryError);
      }
    }
  );
}
