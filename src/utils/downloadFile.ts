import { type AxiosHeaderValue } from "axios";

type Res = {
  headers?: Record<string, AxiosHeaderValue>;
  data: Blob;
};
export function downloadFile(res: Res): void {
  // 优先读取additional-attr-by-file-name字段，其次读取content-disposition字段
  const contentDisposition = res.headers?.["content-disposition"];
  const fileName: string =
    (res.headers?.["additional-attr-by-file-name"] as string) ||
    (typeof contentDisposition === "string"
      ? window.decodeURI(contentDisposition.split("=")[1] || "")
      : "");
  const _filename = fileName.replace(/['"]/g, "");
  const aLink = document.createElement("a");
  const url = window.URL.createObjectURL(res.data);
  aLink.href = url;
  aLink.setAttribute("download", _filename); // 设置下载文件名称
  document.body.appendChild(aLink);
  aLink.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(aLink);
}
