export function isNumber(value: number | string): boolean {
  return !Number.isNaN(parseFloat(String(value))) && isFinite(value as number);
}

// 四舍五入保留小数点  12345.678 => 12345.7
export function roundToDecimal(num: number, decimalPlaces: number | null = 0): number {
  const factor = Math.pow(10, decimalPlaces);
  const tempNum = num * factor;
  const roundedTempNum = Math.round(tempNum);
  return roundedTempNum / factor;
}
/**
 *
 * @param num 入参number
 * @param decimalPlaces 保留小数点，默认1位,亿数量级时保留2位
 * @returns 返回格式化后的数字字符串
 *  * 格式化规则：
 * 1. 小于1万，1234.567 => 1,234.6
 * 2. 小于1亿，123456.789 => 12.3万
 * 3. 大于等于1亿，123456789.123 => 1.23亿
 */
export function formatNumber(num: number | string, decimalPlaces: number | null = 1): string {
  if (num === 0) return "0";
  if (!num) return "";
  if (!isNumber(num)) return num.toString();

  const ruleSteps = [
    {
      baseValue: 10000,
      shrink: 1,
      decimalPlaces: decimalPlaces,
      unit: "",
    },
    {
      baseValue: 100000000,
      shrink: 10000,
      decimalPlaces: decimalPlaces,
      unit: "万",
    },
    {
      baseValue: Infinity,
      shrink: 100000000,
      decimalPlaces: 2, // 亿数量级时保留两位小数
      unit: "亿",
    },
  ];
  const ruleStep = ruleSteps.find((rule) => Math.abs(num as number) < rule.baseValue);

  num = roundToDecimal((num as number) / ruleStep.shrink, ruleStep.decimalPlaces);
  return (
    Number(num)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ",") + ruleStep.unit
  );
}

export const formatNumberWithUnit = (
  num: number,
  unit = "",
  decimalPlaces: number | null = 1
): { num: string; unit: string } => {
  const formattedNum = formatNumber(num, decimalPlaces);
  return formattedNum.includes("万") || formattedNum.includes("亿")
    ? { num: formattedNum.slice(0, -1), unit: `${formattedNum.slice(-1)}${unit}` }
    : { num: formattedNum, unit };
};
