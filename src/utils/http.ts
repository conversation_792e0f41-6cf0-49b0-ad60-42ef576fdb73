import axios from "axios";
import { useUserStore } from "@/stores/user.js";
import { ConcurrencyManager } from "@/api/axios-concurrency.ts";
import { setupAxiosRetry, type RetryConfig } from "./axios-retry.ts";
import notify from "@/components/ui/PureNotify/utils/notify.js";

const BUSINESS_ERROR_CODES = {
  NO_RETRY: [
    10011, //无权限，重试无意义
    10001, //参数错误，重试无意义
    10030, //  不允许的RTM， 重试无意义
  ],
  RETRYABLE: [
    10000, // SERVICE INTERNAL ERROR - 服务内部错误，可能是临时的
    10022, // DB INSERT ERROR - 数据库插入错误，可能是临时的
    10024, // DB DELETE ERROR - 数据库删除错误，可能是临时的
  ],
} as const;

// 从环境变量获取配置
const apiConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || "/mybusiness",
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,

  enableConsoleLog: import.meta.env.VITE_ENABLE_CONSOLE_LOG === "true",
  showDebugInfo: import.meta.env.VITE_SHOW_DEBUG_INFO === "true",
  uploadMaxSize: parseInt(import.meta.env.VITE_UPLOAD_MAX_SIZE) || 10485760,
};

/**
 * 检查业务错误码是否应该重试
 */
function shouldRetryBusinessError(errorCode: number): boolean {
  // 不需要重试的错误码
  if (BUSINESS_ERROR_CODES.NO_RETRY.includes(errorCode)) {
    return false;
  }

  // 需要重试的错误码
  if (BUSINESS_ERROR_CODES.RETRYABLE.includes(errorCode)) {
    return true;
  }

  // 未定义的业务错误码，保守起见不重试
  return false;
}

// 重试配置
const retryConfig: RetryConfig = {
  retries: 3, // 重试次数
  retryDelay: 1000, // 重试延迟时间
  exponentialBackoff: true, // 指数退避
  maxDelay: 15000, // 最大延迟时间

  // 根据业务错误码和HTTP状态码判断 是否重试
  retryCondition: (error) => {
    // 检查业务错误码
    if (
      error.response?.data &&
      typeof error.response.data === "object" &&
      "code" in error.response.data
    ) {
      const businessCode = (error.response.data as any).code;
      if (typeof businessCode === "number") {
        const shouldRetry = shouldRetryBusinessError(businessCode);
        if (apiConfig.enableConsoleLog) {
          console.log(`🔍 业务错误码检查 [${businessCode}]: ${shouldRetry ? "可重试" : "不重试"}`);
        }
        return shouldRetry;
      }
    }

    //  HTTP 状态码检查
    if (error.response) {
      const status = error.response.status;

      // 排除4xx 客户端错误 除了特定的几个
      if (status >= 400 && status < 500) {
        const retryableStatus = [408, 429]; // 超时和限流可以重试
        return retryableStatus.includes(status);
      }

      // 5xx 服务器错误通常应该重试
      if (status >= 500) {
        return true;
      }

      return false;
    }

    // 网络错误、超时等应该重试
    if (
      !error.response ||
      error.code === "ECONNABORTED" ||
      error.code === "ETIMEDOUT" ||
      error.code === "ENOTFOUND" ||
      error.code === "ECONNRESET" ||
      error.code === "ENETUNREACH"
    ) {
      return true;
    }

    return false;
  },

  // 重试回调
  onRetry: (retryNumber, error, config) => {
    if (apiConfig.enableConsoleLog) {
      // 安全获取业务错误码
      let businessCode: number | undefined;
      if (
        error.response?.data &&
        typeof error.response.data === "object" &&
        "code" in error.response.data
      ) {
        businessCode = (error.response.data as any).code;
      }

      console.warn(
        `🔄 重试请求 [${retryNumber}/${retryConfig.retries}]: ${config.method?.toUpperCase()} ${config.url}`,
        {
          error: error.message,
          httpStatus: error.response?.status,
          businessCode: businessCode,
          reason: businessCode
            ? `业务错误码: ${businessCode}`
            : `HTTP状态: ${error.response?.status || "网络错误"}`,
        }
      );
    }
  },
};

// 权限错误处理函数
function handlePermissionError(message = "NO PERMISSION") {
  console.warn("🚫 HTTP拦截器检测到权限错误:", message);

  const userStore = useUserStore();
  userStore.clearAllPermissions();
}

const http = axios.create({
  baseURL: apiConfig.baseURL, // 基础 URL
  timeout: apiConfig.timeout, // 请求超时时间
  headers: {
    "Content-Type": "application/json",
  },
});

ConcurrencyManager(http, 5);

// 添加重试功能 注意 必须放在其他拦截器之前
setupAxiosRetry(http, retryConfig);

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const userStore = useUserStore();
    const credentials = userStore.authCredentials; // 从 store 获取凭证

    if (credentials) {
      // 将凭证附加到请求头 必要步骤 否则请求会失败
      config.headers["reseller-id"] = credentials.resellerId;
      config.headers["reseller-rtm"] = credentials.resellerRtm;
      config.headers["shield-ds-prsId"] = credentials.shieldDsPrsId;
      config.headers["shield-ds-prsTypeCode"] = credentials.shieldDsPrsTypeCode;
      config.headers["user-type"] = credentials.userType;
    }

    if (apiConfig.enableConsoleLog) {
      console.log("🚀 发送请求：", config.method?.toUpperCase(), config.url);
      console.log("📋 请求头2：", config.headers);
      if (apiConfig.showDebugInfo) {
        console.log("🔍 完整配置：", config);
      }
    }

    return config;
  },
  (error) => {
    if (apiConfig.enableConsoleLog) {
      console.error("❌ 请求错误：", error);
    }
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    const { data, config } = response;

    // 如果是 blob 类型响应，直接返回完整的 response 对象
    if (config.responseType === "blob" || data instanceof Blob) {
      console.log("🚀 ~ blob download:", data);
      return response; // 返回完整的响应对象，包含 headers
    }

    // todo: 还有别的code 需要对齐
    if (data && data.code === 10011) {
      handlePermissionError(data?.message || "NO PERMISSION");
      const error = new Error(data?.message || "NO PERMISSION");
      error.isPermissionError = true;
      error.code = data.code;
      return Promise.reject(error);
    }

    if (data && typeof data === "object" && "code" in data) {
      // 根据后端约定的状态码处理
      if (data.code === 0) {
        // 成功情况不自动显示消息，由具体业务决定
        return data; // 返回实际数据
      } else {
        // 业务错误
        const message = data.message || "请求失败";
        notify.error(message);
        return Promise.reject(new Error(message));
      }
    }

    // 直接返回数据（如果后端直接返回数据不包装）
    return data;
  },
  (error) => {
    // 注意：这个拦截器会在重试拦截器失败后才执行
    if (apiConfig.enableConsoleLog) {
      console.error("❌ 最终响应错误：", error.message);
      if (error.response) {
        console.error("📨 错误响应：", {
          status: error.response.status,
          statusText: error.response.statusText,
          url: error.config?.url,
          method: error.config?.method?.toUpperCase(),
          data: error.response.data,
          retryCount: error.config?._retryCount || 0,
        });
      } else if (error.request) {
        console.error("📤 请求失败：", error.request);
      }
    }

    if (error.response) {
      const { status, data } = error.response;

      // todo: 临时方案需要修改 需要约定错误码
      if (data && data.code === 10011) {
        handlePermissionError(data.message);
        const permissionError = new Error(data.message || "NO PERMISSION");
        permissionError.isPermissionError = true;
        permissionError.code = data.code;
        return Promise.reject(permissionError);
      }

      switch (status) {
        case 401:
          // HTTP 401 权限问题
          handlePermissionError("访问权限不足");
          break;

        case 403:
          notify.error("禁止访问");
          break;

        case 404:
          notify.error("请求的资源不存在");
          break;

        case 500:
          notify.error("服务器内部错误");
          break;

        case 502:
        case 503:
        case 504:
          notify.error("服务器暂时不可用，请稍后重试");
          break;

        default:
          notify.error(data?.message || error.message || "网络错误");
      }
    } else if (error.request) {
      // 网络错误
      notify.error("网络连接失败，请检查网络设置");
    } else {
      // 其他错误
      notify.error(error.message || "发生未知错误");
    }

    return Promise.reject(error);
  }
);

// 常用请求方法

// 通用请求方法
export const request = (config: any) => {
  return http(config);
};

// 支持单独配置重试的请求方法
export const get = <T>(url: string, params = {}, retryOptions?: RetryConfig) => {
  return http.get<T>(url, {
    params,
    retryConfig: retryOptions,
  });
};

export const post = <T>(url: string, data = {}, retryOptions?: RetryConfig) => {
  return http.post<T>(url, data, {
    retryConfig: retryOptions,
  });
};

export const put = <T>(url: string, data = {}, retryOptions?: RetryConfig) => {
  return http.put<T>(url, data, {
    retryConfig: retryOptions,
  });
};

export const del = <T>(url: string, params = {}, retryOptions?: RetryConfig) => {
  return http.delete<T>(url, {
    params,
    retryConfig: retryOptions,
  });
};

// 特殊场景的请求方法
export const requestWithoutRetry = <T>(config: any): Promise<T> => {
  return http({ ...config, retryConfig: { retries: 0 } });
};

export const requestWithCustomRetry = <T>(config: any, retryConfig: RetryConfig): Promise<T> => {
  return http({ ...config, retryConfig });
};

export { apiConfig, retryConfig };

export default http;
