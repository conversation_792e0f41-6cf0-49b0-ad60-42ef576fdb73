/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSkeletonItem: typeof import('element-plus/es')['ElSkeletonItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
