// gulpfile.js
import gulp from "gulp";
import {
  removeDist,
  buildRootStyle,
  buildStyle,
  buildComponent,
  buildTailwindStyle,
} from "./index.js";

const { series, watch } = gulp;

const srcFiles = "../src/**/*.{js,ts,vue,css,scss}";

export const watchSrc = () => {
  watch(srcFiles, series(
    buildComponent,
    buildStyle,
    buildRootStyle,
    buildTailwindStyle
  ));
};

export default series(
  removeDist,
  buildComponent,
  buildStyle,
  buildRootStyle,
  buildTailwindStyle,
);
