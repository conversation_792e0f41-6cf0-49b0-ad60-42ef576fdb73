{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "emitDeclarationOnly": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "strict": false, "allowJs": true, "checkJs": false, "composite": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "element-plus/global"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}