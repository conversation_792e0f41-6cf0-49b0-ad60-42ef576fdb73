<script setup lang="ts">
import { RankCardProps } from "./props";
import { DmpFlexCard } from "../flexCard";
import { DmpTabSwitcher } from "../tabSwitcher";
import { DmpEmpty } from "../empty";
import { useRankCard } from "./useRankCard";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";
import { TransferLang } from "../../../common/types";
import { inject, Ref } from "vue";
import { DmpLazyImg } from "../lazyImg";
import { computed } from "vue";

defineOptions({
  name: "DmpRankCard",
});

const tt = inject<Ref<TransferLang>>("tt");
const props = withDefaults(defineProps<RankCardProps>(), {
  id: "",
  rankData: () => [],
  title: "",
  gridSpan: () => ({
    row: 1,
    col: 1,
  }),
  tabs: () => [],
  hideImage: false,
  emptyText: "暂无数据",
  updateTime: "",
});

const emit = defineEmits(["tabChange"]);

const { currentTab, currentRankData, getRankImage, handleTabChange } = useRankCard(props);

const currentTabLabel = computed(() => {
  return props.tabs.find((item) => item.value === currentTab.value)?.label;
});

const hideTabs = computed(() => {
  return props.tabs.length <= 1;
});

const onTabChange = (value: string) => {
  handleTabChange(value);
  emit("tabChange", value);
};
</script>

<template>
  <DmpFlexCard
    class="flex flex-col gap-[8px] w-full overflow-hidden"
    :gridSpan="gridSpan"
    :title="tt(title)"
    :showArrow="false"
  >
    <div class="flex flex-col h-full">
      <DmpTabSwitcher
        v-if="!hideTabs"
        :tabs="tabs"
        :defaultTab="currentTab"
        equalWidth
        @change="onTabChange"
        class="!mx-[8px] !mb-[8px]"
      />

      <template v-if="currentRankData.length > 0">
        <DmpPopoverTrigger :id="`${id}.${currentTab}.${currentTabLabel}`">
          <template v-slot:default="{ active }">
            <div
              class="flex flex-col rounded-[12px] highlightable"
              :class="{ 'active-item': active }"
            >
              <template v-for="item in currentRankData" :key="currentTab + item.id">
                <div
                  class="flex items-center rounded-[12px] justify-between w-full h-[64px] px-[8px] shrink-0"
                >
                  <div class="flex items-center gap-[12px] w-full">
                    <div class="w-[24px] shrink-0">
                      <img
                        v-if="item.rank <= 10"
                        :src="getRankImage(item.rank)"
                        :alt="`rank-${item.rank}`"
                        :class="['w-full h-full object-cover  mx-[auto]']"
                      />
                    </div>
                    <div class="flex-1 flex items-center gap-[12px] min-w-0">
                      <div
                        v-if="!hideImage"
                        class="w-[48px] h-[48px] rounded-[8px] shrink-0 flex items-center justify-center overflow-hidden"
                      >
                        <DmpLazyImg
                          :src="item.imageUrl"
                          :alt="item.title"
                          class="w-full h-full object-cover"
                        />
                      </div>
                      <div class="flex flex-col gap-[2px] font-medium flex-1 min-w-0">
                        <div
                          class="text-[13px] text-[#1C1C1E] truncate text-ellipsis overflow-hidden leading-[16px]"
                          :title="item.title"
                        >
                          {{ item.title }}
                        </div>
                        <div
                          class="text-[12px] text-[#6E6E73] truncate text-ellipsis overflow-hidden leading-[16px]"
                          :title="item.subTitle"
                        >
                          {{ item.subTitle }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </template>
        </DmpPopoverTrigger>
      </template>
      <DmpEmpty v-else :text="props.emptyText" />

      <div
        v-if="props.updateTime"
        class="mt-auto pt-[4px] px-[8px] text-[11px] leading-[16px] text-[rgba(28,28,30,.4)]"
      >
        {{ props.updateTime }}
      </div>
    </div>
  </DmpFlexCard>
</template>
