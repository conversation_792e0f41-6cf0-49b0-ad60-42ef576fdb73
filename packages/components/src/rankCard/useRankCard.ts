import { ref, computed } from "vue";
import { RankCardProps } from "./props";

// 预加载rank图片
import rank1 from "@assets/icons/rank/1.svg?url";
import rank2 from "@assets/icons/rank/2.svg?url";
import rank3 from "@assets/icons/rank/3.svg?url";
import rank4 from "@assets/icons/rank/4.svg?url";
import rank5 from "@assets/icons/rank/5.svg?url";
import rank6 from "@assets/icons/rank/6.svg?url";
import rank7 from "@assets/icons/rank/7.svg?url";
import rank8 from "@assets/icons/rank/8.svg?url";
import rank9 from "@assets/icons/rank/9.svg?url";
import rank10 from "@assets/icons/rank/10.svg?url";

export function useRankCard(props: RankCardProps) {
  const currentTab = ref(props.tabs[0]?.value || "");

  // rank图片映射
  const rankImages: Record<number, string> = {
    1: rank1,
    2: rank2,
    3: rank3,
    4: rank4,
    5: rank5,
    6: rank6,
    7: rank7,
    8: rank8,
    9: rank9,
    10: rank10,
  };

  // 获取rank图片
  const getRankImage = (rank?: number): string => {
    return rankImages[rank as number];
  };

  const handleTabChange = (value: string) => {
    currentTab.value = value;
  };

  const currentRankData = computed(() => {
    return props.rankData
      .filter((item) => [item.name, item.lob].includes(currentTab.value))
      .slice(0, 3);
  });

  return {
    currentTab,
    currentRankData,
    getRankImage,
    handleTabChange,
  };
}
