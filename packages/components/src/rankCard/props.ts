export interface RankItem {
  id: string | number;
  title: string;
  imageUrl?: string;
  hideArrow?: boolean;
  name?: string;
  rank?: number;
  subTitle?: string;
  lob?: string;
}
export interface GridSpan {
  row: number;
  col: number;
}

export interface TabItem {
  label: string;
  value: string;
}

export interface ProductRankItem {
  rank: number;
  sub_lob: string;
  storsize_long_desc: string;
  color_long_desc: string;
  [key: string]: any; // 其它字段
}

export interface ProductRankData {
  lob: string;
  data: ProductRankItem[];
}

export interface RankCardProps {
  id: string;
  rankData: RankItem[];
  title?: string;
  gridSpan?: GridSpan;
  tabs?: TabItem[];
  hideImage?: boolean;
  emptyText?: string;
  updateTime?: string;
}
