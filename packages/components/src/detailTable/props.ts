/*
 * @Date: 2025-07-10 13:25:54
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-30 16:09:20
 * @FilePath: /MyBusinessV2/packages/components/src/detailTable/props.ts
 */
export interface DetailTableRow {
  [key: string]: string | number | boolean;
  highlight?: boolean; // 可选，是否高亮
}
export interface DetailTableProps {
  title?: string; // 可选，表格标题
  columns: { label: string; prop: string; align?: string; width?: number }[]; // 必填，表格列配置
  data: DetailTableRow[]; // 必填，表格数据
  unitMap?: { [key: string]: string }; // 可选，单位映射
  highlightRule?: (
    // 可选，高亮规则
    row: DetailTableRow,
    col: { label: string; prop: string }
  ) => boolean;
  loading?: boolean;
  heightClass?: string;
  emptyText?: string; // 可选，空数据时显示的文本
  UnitPresetMode?: string[]; // 可选，单位预设模式
  formatter?: (row: DetailTableRow, col: { label: string; prop: string }) => string; // 可选，格式化函数
}
