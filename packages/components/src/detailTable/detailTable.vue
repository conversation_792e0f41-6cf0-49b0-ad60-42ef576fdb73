<!--
 * @Date: 2025-07-10 13:20:48
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-30 16:16:44
 * @FilePath: /MyBusinessV2/packages/components/src/detailTable/detailTable.vue
-->
<script setup lang="ts">
import { defineProps, computed, Ref, inject } from "vue";
import { DetailTableProps, DetailTableRow } from "@/detailTable/props";
import { ElTable, ElTableColumn } from "element-plus";
import { formatNumber } from "../../../common/utils/number.ts";
import { DmpEmpty } from "../empty";
import { TransferLang } from "../../../common/types";

defineOptions({ name: "DmpDetailTable" });

const tt = inject<Ref<TransferLang>>("tt");
const props = defineProps<DetailTableProps>();

const unitMap = props.unitMap || {};

const getHighlight = (row: DetailTableRow, col: { label: string; prop: string }) => {
  if (typeof props.highlightRule === "function") {
    return props.highlightRule(row, col);
  }
  // 默认：如果 row.highlight 为 true，则高亮
  return !!row.highlight;
};

const heightClass = computed(() => {
  return props.heightClass ?? "h-[116px]";
});

const getLanguage = computed(() => {
  return (key: string) => {
    if (key && tt.value) {
      return tt.value(key);
    }
    return key; // 如果没有找到对应的翻译，返回原始键
  };
});
</script>

<template>
  <div class="dmp-detail-table">
    <div class="font-medium text-[14px] text-[#1c1c1e] mb-[8px] leading-[20px]" v-if="props.title">
      {{ props.title }}
    </div>
    <el-skeleton animated :class="heightClass" :loading="props.loading">
      <template #template>
        <el-skeleton-item variant="rect" class="h-full rounded-[12px] !card-skeleton" />
      </template>
      <template #default>
        <template v-if="props.data.length">
          <el-table :data="props.data" :empty-text="props.emptyText">
            <el-table-column
              v-for="(col, idx) in props.columns"
              :key="col.prop"
              :prop="col.prop"
              :label="col.label"
              :width="col.width"
            >
              <template #default="scope">
                <slot :name="col.prop" :row="scope.row" :index="scope.$index">
                  <div :class="['flex items-center', 'h-[24px]']">
                    <span
                      :title="scope.row[col.prop]"
                      :class="[
                        'text-[#1C1C1E] truncate text-ellipsis overflow-hidden block',
                        { 'font-bold': idx !== 0 },
                        {
                          'highlight text-[#F63F54]':
                            getHighlight(scope.row, col) && scope.row[col.prop] !== null,
                        },
                        {
                          '!leading-[16px]': props.UnitPresetMode?.includes(col.prop),
                        },
                        { 'text-[12px]': idx === 0 },
                        { 'text-[13px]': idx !== 0 },
                      ]"
                    >
                      {{
                        col.prop === "cn_name"
                          ? tt(scope.row[col.prop])
                          : col.formatter
                            ? col.formatter(scope.row)
                            : formatNumber(scope.row[col.prop])
                      }}
                    </span>
                    <span
                      v-if="scope.row[col.prop] != null && scope.row[col.prop] !== ''"
                      :class="[
                        'text-[10px] ml-[2px]',
                        'leading-[normal]',
                        'text-[rgba(28,28,30,.6)]',
                        {
                          '!mt-[2px]': props.UnitPresetMode?.includes(col.prop),
                        },
                        { 'highlight !text-[rgba(246,63,84,0.6)]': getHighlight(scope.row, col) },
                      ]"
                    >
                      {{ getLanguage(unitMap[col.prop]) }}
                    </span>
                  </div>
                </slot>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <div
          v-else
          class="flex items-center justify-center rounded-[12px]"
          :class="heightClass"
          style="background: rgba(28, 28, 30, 0.06)"
        >
          <DmpEmpty />
        </div>
      </template>
    </el-skeleton>
  </div>
</template>
