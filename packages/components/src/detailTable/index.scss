.dmp-detail-table {
  .el-table {
    background: rgba(28, 28, 30, 0.06) !important;
    border-radius: 12px;
    width: 100%;
    .el-table__inner-wrapper {
      padding-bottom: 8px;
      &::before {
        background: unset;
      }
    }
  }
  .el-table tr,
  .el-table th,
  .el-table td {
    background: transparent !important;
  }
  .el-table td {
    border: none !important;
    color: #1c1c1e !important;
    font-size: 11px !important;
    padding: 0 !important;
    vertical-align: middle !important;
    .cell {
      height: 100%;
      height: 24px;
      padding: 0 8px !important;
    }
    &:first-child {
      .cell {
        padding-left: 12px !important;
      }
    }
  }
  .el-table th {
    color: #1c1c1e !important;
    font-size: 11px !important;
    height: 36px !important;
    line-height: 36px !important;
    padding: 0 !important;
    font-weight: normal !important;
    border: none !important;
    .cell {
      font-size: 12px !important;
      padding: 0 8px !important;
    }
    &:first-child {
      .cell {
        padding-left: 12px !important;
      }
    }
  }
  .el-table__header {
    width: 100% !important;
    position: relative;
    &::after {
      content: "";
      display: block;
      position: absolute;
      left: 12px;
      right: 12px;
      bottom: 0;
      height: 1px;
      background: rgba(28, 28, 30, 0.08);
      z-index: 1;
    }
  }
  .el-scrollbar__view {
    width: 100% !important;
  }
  .el-table__body {
    width: 100% !important;
  }
  .hightlight {
    color: #f63f54 !important;
  }
  .el-table__cell.is-right .cell {
    justify-content: flex-end !important;
  }
  .el-table__body-wrapper {
    margin-top: 6px !important;
  }
}
