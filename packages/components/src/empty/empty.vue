<script setup lang="ts">
import { EmptyProps } from "@/empty/props";
import defaultNoDataImage from "./assets/nodata.png";
import { inject, defineOptions, computed, Ref } from "vue";
import { TransferLang } from "../../../common/types";

defineOptions({ name: "DmpEmpty" });

const tt = inject<Ref<TransferLang>>("tt");

const { image = defaultNoDataImage, text = "暂无数据" } = defineProps<EmptyProps>();
</script>

<template>
  <div class="h-full w-full flex flex-col items-center justify-center gap-[4px] p-[4px]">
    <div class="flex flex-col items-center justify-start gap-[6px] opacity-40 text-[#1c1c1e]">
      <slot name="image">
        <div class="empty-image flex-shrink-0">
          <img :src="image" alt="nodata" class="w-[40px] object-contain" />
        </div>
      </slot>

      <slot name="text">
        <div class="empty-text text-center">
          <p class="text-[13px] text-normal">{{ tt(text) }}</p>
        </div>
      </slot>
    </div>
  </div>
</template>

<style scoped></style>
