<script setup lang="ts">
import { AlertStatusListProps } from "./props";
import DmpAlertStatusItem from "@/alertStatusItem";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";

defineOptions({
  name: "DmpAlertStatusList",
});

const props = withDefaults(defineProps<AlertStatusListProps>(), {
  items: () => [],
  isMobile: false,
});
</script>

<template>
  <div class="alert-status-list">
    <el-scrollbar class="h-full">
      <div class="relative">
        <template v-for="(item, index) in props.items" :key="item.id">
          <DmpPopoverTrigger :id="`${item.id}`">
            <template v-slot:default="{ active }">
              <div
                :data-item-id="item.id"
                :class="[
                  'relative z-10 px-[8px] rounded-[8px] highlightable',
                  { 'active-item': active },
                ]"
              >
                <dmp-alert-status-item
                  :id="item.id"
                  :title="item.title"
                  :progress="item.progress"
                  :color="item.color"
                  :status-text="item.statusText"
                  :gradient="item.gradient"
                  :status-level="item.statusLevel"
                  :show-text="item.showText"
                  :progress-unit="item.progressUnit"
                  :warning-cnt="item.warningCnt"
                  :text-color="item.textColor"
                  :is-mobile="props.isMobile"
                />
                <div
                  v-if="index !== props.items.length - 1"
                  class="border-horizontal bg-[rgba(28,28,30,0.1)] group-line"
                ></div>
              </div>
            </template>
          </DmpPopoverTrigger>
        </template>
      </div>
    </el-scrollbar>
  </div>
</template>
