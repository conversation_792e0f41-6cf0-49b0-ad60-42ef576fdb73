<!--
 * @Date: 2025-07-23 18:44:22
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-23 19:09:10
 * @FilePath: /MyBusinessV2/packages/components/src/lottiePlayer/lottiePlayer.vue
-->
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import lottie, { AnimationItem } from "lottie-web";
import { LottiePlayerProps } from "./props";

const props = defineProps<LottiePlayerProps>();

const lottieContainer = ref<HTMLElement | null>(null);
let animation: AnimationItem | null = null;

const playLottie = () => {
  if (animation) {
    animation.destroy();
    animation = null;
  }
  if (lottieContainer.value && props.animationData) {
    animation = lottie.loadAnimation({
      container: lottieContainer.value,
      renderer: "svg",
      loop: props.loop ?? true,
      autoplay: props.autoplay ?? true,
      animationData: props.animationData,
    });
  }
};

onMounted(playLottie);
onBeforeUnmount(() => {
  if (animation) animation.destroy();
});
watch(() => props.animationData, playLottie);
</script>

<template>
  <div
    ref="lottieContainer"
    :style="{
      width: typeof width === 'number' ? width + 'px' : width || '56px',
      height: typeof height === 'number' ? height + 'px' : height || '56px',
    }"
  ></div>
</template>
