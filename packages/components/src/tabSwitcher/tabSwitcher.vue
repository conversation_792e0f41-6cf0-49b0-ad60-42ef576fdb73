<script setup lang="ts">
import { TabSwitcherProps } from "./props";
import { useTabSwitcher } from "./useTabSwitcher";

const props = withDefaults(defineProps<TabSwitcherProps>(), {
  animation: true,
  tabs: () => [
    { label: "Tab 1", value: "tab1" },
    { label: "Tab 2", value: "tab2" },
    { label: "Tab 3", value: "tab3" },
  ],
  equalWidth: true,
  disabled: false,
  bgColor: "rgba(28, 28, 30, 0.06)",
  defaultTab: null,
  disabledTabs: () => [],
  mode: "large",
  showDivider: false,
  btnClass: "",
  tabHeight: 40,
  defaultPadding: 4,
  slideClass: "",
  tabHeightClass: "",
});

const emit = defineEmits(["change", "update:defaultTab"]);

const {
  tabButtons,
  tabsWrapper,
  activeTab,
  changeTab,
  updateSlidePosition,
  slideTargetWidth,
  translateX,
} = useTabSwitcher(props, emit);
</script>

<template>
  <div
    class="tab-switcher-wrap relative h-[40px]"
    :class="{
      [props.mode]: true,
      'pointer-events-none opacity-50': props.disabled,
      [props.tabHeightClass]: props.tabHeightClass,
    }"
  >
    <div
      class="relative w-full h-[40px] tabs-bg"
      :class="[props.mode, props.tabHeightClass]"
      :style="{ backgroundColor: props.bgColor }"
    >
      <!-- Tab buttons -->
      <div
        class="relative w-full h-full flex items-center z-10 p-[4px]"
        :ref="(el) => (tabsWrapper = el as HTMLElement)"
      >
        <div
          v-for="(tab, index) in props.tabs"
          :key="index"
          class="flex items-center h-full"
          :ref="(el) => (tabButtons[index] = el as HTMLElement)"
          :class="[props.equalWidth ? 'flex-1 min-w-0' : '']"
          :style="props.equalWidth ? { width: `${100 / props.tabs.length}%` } : {}"
        >
          <slot
            name="btns"
            :tab="tab"
            :activeTab="activeTab"
            :index="index"
            :key="tab.value ?? index"
          >
            <button
              class="w-full text-[12px] text-center transition-all font-normal duration-200 text-[#1C1C1E] h-[32px] overflow-hidden whitespace-nowrap text-ellipsis px-[8px]"
              :class="[
                activeTab === tab.value ? 'text-#1C1C1E' : 'font-normal',
                props.disabledTabs.includes(tab.value) ? 'disabled-tab' : '',
                props.equalWidth ? 'min-w-0' : 'flex-1',
                btnClass,
              ]"
              @click="changeTab(tab.value, index)"
            >
              <slot name="tab" :tab="tab" :index="index">
                {{ tab.label }}
              </slot>
            </button>
            <div
              class="line flex-0-auto w-[1px] h-[20px] bg-#E5E5EA"
              v-if="props.showDivider && index + 1 !== props.tabs.length"
            ></div>
          </slot>
        </div>
      </div>

      <!-- Sliding indicator -->
      <div
        :class="[
          'absolute h-[32px] bg-white shadow-[0px_0px_6px_0px_rgba(0,0,0,0.08)] mask-box',
          animation ? 'transition-all duration-200 ease-in-out' : '',
          slideClass,
        ]"
        :style="{
          top: '50%',
          width: `${slideTargetWidth}px`,
          transform: `translateY(-50%) translateX(${translateX}px)`,
        }"
      ></div>
    </div>
  </div>
</template>
