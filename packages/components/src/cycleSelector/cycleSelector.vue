<template>
  <div class="dmp-cycle-selector relative w-full">
    <!-- 箭头按钮 -->
    <button
      class="absolute left-0 top-[4px] w-[24px] h-[32px] flex items-center justify-center rounded-[8px] transition z-20"
      :disabled="isFirst"
      @click="handlePrev"
      aria-label="上一周期"
      :class="[
        isFirst ? 'bg-[rgba(28,28,30,0.06)]' : 'bg-white shadow-[0_0_4px_0_rgba(0,0,0,0.08)]',
      ]"
    >
      <Icon name="arrow-right" class="rotate-180" :class="isFirst ? 'opacity-40' : ''" />
    </button>

    <div
      class="flex items-center overflow-x-auto scrollbar-hide min-w-0 gap-[4px] h-[40px] box-border mx-[24px] px-[4px] relative z-3"
      ref="listRef"
    >
      <template v-for="(item, index) in cycles" :key="index + item?.value">
        <button
          class="transition-all duration-150 px-[10px] py-0 flex items-center text-[12px] leading-[32px] text-[#3A3A3C] font-regular min-w-fit rounded-[8px] relative z-[2] box-border"
          :class="[
            item?.value === selected ? '!font-medium' : 'hover:bg-[rgba(28,28,30,0.06)]',
            item?.value === currentWeek ? '!text-[#0071E3]' : '!text-[#1C1C1E]',
          ]"
          @click="handleSelect(item?.value)"
          :ref="(el) => (cycleRefs[index] = el as HTMLElement)"
        >
          {{ item?.label ?? item?.value }}
        </button>
      </template>
      <div
        class="slider bg-white shadow-[0_0_4px_0_rgba(0,0,0,0.08)] h-[32px] transition-all duration-200 absolute z-[0] top-[4px] rounded-[8px]"
        ref="sliderRef"
        :style="{ width: `${sliderWidth}px`, transform: `translateX(${sliderTranslateX}px)` }"
      ></div>
    </div>

    <!-- 右箭头 -->
    <button
      class="absolute right-0 top-[4px] w-[24px] h-[32px] flex items-center justify-center rounded-[8px] transition z-20"
      :disabled="isLast"
      @click="handleNext"
      aria-label="下一周期"
      :class="[
        isLast ? 'bg-[rgba(28,28,30,0.06)]' : 'bg-white shadow-[0_0_4px_0_rgba(0,0,0,0.08)]',
      ]"
    >
      <Icon name="arrow-right" :class="isLast ? 'opacity-40' : ''" />
    </button>
  </div>
</template>

<script setup lang="ts">
import {
  toRefs,
  ref,
  computed,
  defineProps,
  defineEmits,
  watch,
  nextTick,
  withDefaults,
} from "vue";
import { Props, Cycle } from "@/cycleSelector/props";
import { Icon } from "../icon/icon";

defineOptions({ name: "DmpCycleSelector" });
const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  cycles: () => [],
  currentWeek: "",
});
const { modelValue, currentWeek } = toRefs(props);

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
}>();

const selected = computed({
  get() {
    return modelValue.value;
  },
  set(val) {
    emit("update:modelValue", val);
    emit("change", val);
  },
});

const cycles = computed(() => {
  return props.cycles.map((item: Cycle[number]) => {
    if (typeof item === "string") {
      return { label: item, value: item };
    }
    return item;
  });
});

const currentIndex = computed(() => {
  return cycles.value.findIndex((item) => item?.value === selected.value);
});

const isFirst = computed(() => {
  return currentIndex.value <= 0;
});

const isLast = computed(() => {
  return currentIndex.value >= cycles.value.length - 1;
});

// 选中项
function handleSelect(value: string) {
  if (value === selected.value) return;
  const index = cycles.value.findIndex((item) => item?.value === value);
  if (index < 0) return;
  selected.value = value;
}

// 左右切换
function handlePrev() {
  if (!isFirst.value) {
    handleSelect(cycles.value[currentIndex.value - 1]?.value);
  }
}
function handleNext() {
  if (!isLast.value) {
    handleSelect(cycles.value[currentIndex.value + 1]?.value);
  }
}

// 滚动居中到选中项
const cycleRefs = ref<HTMLElement[]>([]);
const listRef = ref<HTMLElement | null>(null);
const sliderRef = ref<HTMLElement | null>(null);
const sliderWidth = ref(0);
const sliderTranslateX = ref(0);

watch(
  () => selected.value,
  async (value) => {
    if (!value) return;
    await nextTick();
    const el = cycleRefs.value[currentIndex.value];
    const list = listRef.value;
    if (el && list) {
      el.scrollIntoView({ behavior: "smooth", block: "center", inline: "center" });
      sliderWidth.value = el.offsetWidth;
      sliderTranslateX.value = el.offsetLeft - 4;
    }
  },
  { immediate: true }
);
</script>
