<script setup lang="ts">
import { PopoverTriggerProps } from "@/popoverTrigger/props.ts";
import DmpPopover from "@/popover/popover.vue";
import { computed, inject, toRefs, ref, Ref, watch, onBeforeUnmount } from "vue";
import { ClickOutside as vClickOutside } from "element-plus";
import { CompRegistry, Schema } from "../../../common/types";
import { comparePaths, getMatches, replaceMatches } from "@/utils";
import { Icon } from "../icon/icon";

defineOptions({ name: "DmpPopoverTrigger" });
const compsRegistry = inject<CompRegistry>("compsRegistry");
const schema = inject<Ref<Schema>>("schema");
const isMobile = inject<Ref<boolean>>("isMobile");
const { id } = defineProps<PopoverTriggerProps>();

const created = ref(false);
const show = ref(false);

// 移动端禁止body滚动
let originalBodyOverflow = "";
let originalBodyHeight = "";

// 如果是移动端并且弹窗打开，则禁止body滚动
watch(
  () => isMobile?.value && created.value && show.value,
  (shouldPreventScroll) => {
    if (!isMobile?.value) return;

    if (shouldPreventScroll) {
      // 保存原始样式
      originalBodyOverflow = document.body.style.overflow;
      originalBodyHeight = document.body.style.height;

      // 阻止页面滚动
      document.body.style.overflow = "hidden";
      document.body.style.height = "100vh";
    } else {
      // 恢复原始样式
      document.body.style.overflow = originalBodyOverflow;
      document.body.style.height = originalBodyHeight;
    }
  },
  { immediate: true }
);

// 恢复body滚动
onBeforeUnmount(() => {
  if (isMobile?.value && document.body) {
    document.body.style.overflow = originalBodyOverflow;
    document.body.style.height = originalBodyHeight;
  }
});

const foundKey = computed(() =>
  Object.keys(schema.value?.popovers || {})
    .reverse()
    .find((key) => comparePaths(key, id))
);
const matches = computed(() => getMatches(foundKey?.value, id));
const popoverConfig = computed(() => schema.value?.popovers?.[foundKey?.value]);
const { type, title, extraIcon, extraTitle, props } = toRefs(popoverConfig?.value);

const finalTitle = computed(() => replaceMatches(title?.value, matches?.value));
const finalExtraIcon = computed(() => replaceMatches(extraIcon?.value, matches?.value));
const finalExtraTitle = computed(() => replaceMatches(extraTitle?.value, matches?.value));
const finalProps = computed(
  () => props && JSON.parse(replaceMatches(JSON.stringify(props.value), matches?.value))
);

const popoverCompName = computed(() => type?.value);
const moduleName = computed(() => id?.split(".")?.[0] ?? "");

const onClickOutside = () => {
  show.value = false;
};

const clickShow = () => {
  if (schema.value) {
    created.value = true;
    show.value = true;
  }
};
</script>
<template>
  <dmp-popover
    class="dmp-popover"
    :title="finalTitle"
    :extra-icon="finalExtraIcon"
    :extra-title="finalExtraTitle"
    trigger="click"
    :visible="created && show"
  >
    <template #reference>
      <div @click="clickShow">
        <slot :active="created && show" />
      </div>
    </template>
    <component
      v-if="compsRegistry?.[popoverCompName] && created"
      class="dmp-popover-trigger"
      :is="compsRegistry[popoverCompName]"
      v-bind="finalProps"
      :module-name="moduleName"
      v-click-outside="onClickOutside"
    />
    <div
      class="close w-[24px] h-[24px] absolute top-[20px] right-[20px] flex items-center justify-center z-9 cursor-pointer"
      @click="show = false"
      v-if="isMobile"
    >
      <Icon name="close" class="text-[#1C1C1E] text-[14px]" />
    </div>
  </dmp-popover>
  <teleport to="body">
    <div v-if="isMobile && created && show" class="fixed inset-0 z-2023 bg-[rgba(0,0,0,0.4)]"></div>
  </teleport>
</template>
