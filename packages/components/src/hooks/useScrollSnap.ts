import { computed, ref, watch, nextTick, onUnmounted, Ref } from "vue";

export const useScrollSnap = <T>(
  list: Ref<T[]>,
  itemsPerScreen: Ref<number>,
  scrollContainer: Ref<HTMLDivElement | null>,
  scrollItemRefs: Ref<HTMLElement[]>
) => {
  // 按每屏数量分组数据
  const groupList: Ref<T[][]> = computed(() => {
    const groups = [];
    const total = list.value.length;
    for (let i = 0; i < total; i += itemsPerScreen.value) {
      groups.push(list.value.slice(i, i + itemsPerScreen.value));
    }
    return groups;
  });

  // 计算每屏宽度
  const setGroupWidth = (index: number) => {
    if (groupList.value.length === 1 || index !== groupList.value.length - 1) {
      return { width: "100%" };
    }
    return {
      width: `${(100 / itemsPerScreen.value) * groupList.value[groupList.value.length - 1].length}%`,
    };
  };

  // 设置每屏列数
  const setGroupCol = (length: number) => {
    return {
      "grid-template-columns": `repeat(${length}, 1fr)`,
    };
  };

  // 滚动相关
  const currentScreen = ref(0);
  let observer: IntersectionObserver | null = null;
  const Threshold = 0.9;

  // 监听数据变化，创建IntersectionObserver观察器
  watch(
    [() => groupList.value.length, () => scrollContainer.value, () => scrollItemRefs.value.length],
    async () => {
      if (
        !groupList.value.length ||
        !scrollContainer.value ||
        !scrollItemRefs.value.length ||
        observer
      )
        return;

      await nextTick();
      observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && entry.intersectionRatio > Threshold) {
              const index = Number((entry.target as HTMLElement).dataset.index);
              currentScreen.value = index;
            }
          });
        },
        {
          root: scrollContainer.value,
          threshold: Threshold,
        }
      );

      // 监听每屏
      scrollItemRefs.value.forEach((item) => {
        observer!.observe(item);
      });
    }
  );

  // 点击指示器区域切换到下一屏
  const handleIndicatorClick = () => {
    if (groupList.value.length <= 1) return;

    const nextIndex =
      currentScreen.value === groupList.value.length - 1 ? 0 : currentScreen.value + 1;
    scrollToScreen(nextIndex);
  };

  // 滚动到指定屏
  const scrollToScreen = (index: number) => {
    if (!scrollContainer.value) return;

    scrollContainer.value.scrollTo({
      left: scrollContainer.value.clientWidth * index,
      behavior: "smooth",
    });
  };

  // 清理观察器
  onUnmounted(() => {
    if (observer) {
      observer.disconnect();
      observer = null;
    }
  });

  return {
    groupList,
    currentScreen,
    setGroupWidth,
    setGroupCol,
    handleIndicatorClick,
  };
};
