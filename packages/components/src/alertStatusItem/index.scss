.alert-status-item-title {
  @apply text-[13px] font-normal leading-[16px] text-[#1C1C1E] shrink-0 w-[84px];
}

.alert-status-item-status-text {
  @apply text-[11px] font-medium leading-[16px];
  color: rgba(28, 28, 28, 0.56);
  width: 66px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  height: 20px;

  // 使用伪元素创建背景层
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-color);
    opacity: 0.12;
    border-radius: 10px;
    z-index: -1;
  }
}

.progress-bar {
  position: relative;
  overflow: visible;
  height: 100%;
}
