<script setup lang="ts">
import { AlertStatusItemProps } from "./props";
import { DmpProgressBar } from "../progressBar";
import { inject, Ref } from "vue";
import { TransferLang } from "../../../common/types";

const tt = inject<Ref<TransferLang>>("tt");

defineOptions({
  name: "DmpAlertStatusItem",
});

const emit = defineEmits(["itemClick"]);

const props = withDefaults(defineProps<AlertStatusItemProps>(), {
  id: "",
  title: "",
  statusText: "",
  gradient: () => ["#FF8175", "#F63F54"],
  statusLevel: "normal",
  progress: 0,
  color: "",
  showText: true,
  progressUnit: "",
  warningCnt: 0,
  textColor: "rgba(28,28,30,0.72)",
  isMobile: false,
});

const handleClick = (id: string) => {
  emit("itemClick", id);
};
</script>

<template>
  <div
    class="alert-status-item flex h-[40px] bg-transparent items-center justify-between gap-[12px] box-border overflow-hidden"
    @click="handleClick(props.id)"
  >
    <div :class="['flex justify-between items-center', 'min-w-[82px]']">
      <div class="alert-status-item-title">{{ tt(props.title) }}</div>
    </div>

    <div class="progress-bar flex-1">
      <DmpProgressBar v-bind="props" />
    </div>

    <div
      :class="['alert-status-item-status-text', 'relative']"
      :style="{
        color: props.textColor,
        '--text-color': props.textColor,
      }"
      :title="tt(props.statusText)"
    >
      {{ tt(props.statusText) }}
    </div>
  </div>
</template>
