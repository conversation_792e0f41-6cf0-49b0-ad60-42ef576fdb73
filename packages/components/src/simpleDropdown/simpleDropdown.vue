<!--
 * @Date: 2025-07-15 17:12:06
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-23 18:04:11
 * @FilePath: /MyBusinessV2/packages/components/src/simpleDropdown/simpleDropdown.vue
-->
<script setup lang="ts">
import { ref } from "vue";
import { onClickOutside } from "@vueuse/core";
import ArrowRightIcon from "../../assets/icons/arrow-right.svg";
import { SimpleDropdownProps } from "./props";
import { useDropdownTransition } from "./useDropdownTransition";

defineOptions({ name: "DmpSimpleDropdown" });

const props = withDefaults(defineProps<SimpleDropdownProps>(), {
  options: () => [],
  modelValue: "",
  placement: "bottom",
  btnClass: "",
  dropdownClass: "",
});
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
}>();

const { onEnter, onAfterEnter, onLeave, onAfterLeave } = useDropdownTransition();
const showDropdown = ref(false);
const rootRef = ref<HTMLElement | null>(null);

function selectOption(val: string) {
  emit("update:modelValue", val);
  emit("change", val);
  showDropdown.value = false;
}

onClickOutside(rootRef, () => {
  showDropdown.value = false;
});
</script>

<template>
  <div class="relative inline-block" ref="rootRef">
    <button
      class="py-[2px] px-[8px] rounded-[8px] flex gap-[2px] items-center text-[12px] font-medium leading-[20px] text-[#1C1C1E] transition-colors duration-150 hover:bg-[rgba(28,28,30,0.06)]"
      :class="[props.btnClass, showDropdown ? '!bg-white ' : '']"
      @click="showDropdown = !showDropdown"
      type="button"
    >
      {{ options.find((opt) => opt.value === modelValue)?.label || modelValue }}
      <ArrowRightIcon
        class="w-[14px] h-[14px] transition-all"
        :class="showDropdown ? 'rotate-[-90deg]' : 'rotate-[90deg]'"
      />
    </button>

    <Transition
      @enter="onEnter"
      @after-enter="onAfterEnter"
      @leave="onLeave"
      @after-leave="onAfterLeave"
    >
      <div
        v-if="showDropdown"
        class="absolute rounded-[12px] p-[2px] bg-white/90 shadow-[0px_0px_8px_0px_rgba(0,0,0,0.08)] backdrop-blur-[16px] z-[99] whitespace-nowrap max-w-[200px]"
        :class="[`placement-${props.placement}`, props.dropdownClass]"
      >
        <div
          v-for="opt in options"
          :key="opt.label + opt.value"
          class="px-4 py-2 leading-[16px] mt-[2px] rounded-[10px] cursor-pointer text-[12px] first:mt-0 transition-colors duration-150"
          :class="{
            'text-[#0071E3] bg-[#0071E3]/[0.08] font-medium': opt.value === modelValue,
            'text-[#1C1C1E] hover:bg-[#1c1c1e0f]': opt.value !== modelValue,
          }"
          @click="selectOption(opt.value)"
        >
          {{ opt.label }}
        </div>
      </div>
    </Transition>
  </div>
</template>
