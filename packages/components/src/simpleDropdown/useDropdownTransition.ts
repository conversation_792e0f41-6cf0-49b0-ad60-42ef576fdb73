import { nextTick } from "vue";
/**
 * 下拉动画hooks
 * @param duration 动画持续时间，单位毫秒
 * @param maxHeight 下拉最大高度，单位像素
 * @returns 动画名称
 */
export function useDropdownTransition(duration: number = 150, maxHeight: number = 200) {
  const transition = `max-height ${duration}ms cubic-bezier(0.4,0,0.2,1), opacity ${duration}ms`;

  function onEnter(el: HTMLElement) {
    el.style.maxHeight = "0";
    el.style.opacity = "0";
    el.style.transition = transition;
    nextTick(() => {
      const _maxHeight = Math.min(el.scrollHeight, maxHeight);
      el.style.maxHeight = _maxHeight + "px";
      el.style.opacity = "1";
      el.style.overflow = "hidden";
    });
  }
  function onAfterEnter(el: HTMLElement) {
    el.style.maxHeight = "";
    el.style.opacity = "";
    el.style.transition = "";
  }
  function onLeave(el: HTMLElement) {
    el.style.maxHeight = el.scrollHeight + "px";
    el.style.opacity = "1";
    el.style.transition = transition;
    // 强制重绘
    void el.offsetHeight;
    el.style.maxHeight = "0";
    el.style.opacity = "0";
  }
  function onAfterLeave(el: HTMLElement) {
    el.style.maxHeight = "";
    el.style.opacity = "";
    el.style.transition = "";
  }

  return {
    onEnter,
    onAfterEnter,
    onLeave,
    onAfterLeave,
  };
}
