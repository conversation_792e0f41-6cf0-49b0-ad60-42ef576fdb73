<script setup lang="ts">
import rank1 from "@assets/icons/rank/1.svg?url";
import rank2 from "@assets/icons/rank/2.svg?url";
import rank3 from "@assets/icons/rank/3.svg?url";
import rank4 from "@assets/icons/rank/4.svg?url";
import rank5 from "@assets/icons/rank/5.svg?url";
import rank6 from "@assets/icons/rank/6.svg?url";
import rank7 from "@assets/icons/rank/7.svg?url";
import rank8 from "@assets/icons/rank/8.svg?url";
import rank9 from "@assets/icons/rank/9.svg?url";
import rank10 from "@assets/icons/rank/10.svg?url";
import { ref, toRefs, withDefaults, computed } from "vue";
import { PopoverRankItem, PopoverRankProps } from "./props";
import { DmpEmpty } from "../empty";
import { useLanguageReplace } from "../../../common/hooks/useLanguageReplace";

defineOptions({ name: "DmpPopoverRank" });
const props = withDefaults(defineProps<PopoverRankProps>(), {
  list: (): PopoverRankItem[] => [],
  nameTitle: "",
  valueTitle: "",
  columns: () => ["1fr", "80px"],
  loading: false,
});
const { list, nameTitle, valueTitle, columns, loading } = toRefs(props);
const needTransferWords = ["本周销量", "季度累计销量"];
const { transferLangByWords } = useLanguageReplace();
// rank图片映射
const rankImages = ref({
  1: rank1,
  2: rank2,
  3: rank3,
  4: rank4,
  5: rank5,
  6: rank6,
  7: rank7,
  8: rank8,
  9: rank9,
  10: rank10,
});

const colStyle = computed(() => {
  return {
    "grid-template-columns": `${columns.value.join(" ")}`,
  };
});

const showValue = computed(() => {
  return columns.value.length > 1;
});
</script>

<template>
  <div class="dmp-popover-rank">
    <div
      class="grid items-center gap-x-[12px] text-[12px] leading-[20px] text-[#1C1C1E] mb-[4px]"
      :style="colStyle"
    >
      <div>
        <slot name="nameTitle">{{ nameTitle }}</slot>
      </div>
      <div v-if="showValue">
        <slot name="valueTitle">{{ valueTitle }}</slot>
      </div>
    </div>
    <el-skeleton animated class="h-full" :loading>
      <template #template>
        <el-skeleton-item variant="rect" class="h-full rounded-[12px] card-skeleton" />
      </template>
      <template #default>
        <template v-if="list.length">
          <div
            class="grid items-center gap-x-[24px] h-[56px] box-border cursor-default"
            :style="colStyle"
            v-for="item in list"
            :key="item.rank"
          >
            <div class="flex items-center gap-[12px] overflow-hidden">
              <div class="w-[24px] h-[24px] shrink-0">
                <img
                  v-if="item.rank <= 10"
                  :src="rankImages[item.rank]"
                  class="w-full h-full object-cover block"
                />
                <span v-else class="text-[12px] leading-[16px] text-[#1C1C1E]">{{
                  item.rank
                }}</span>
              </div>
              <div class="min-w-0">
                <slot name="name" :name="{ name: item.name, desc: item.desc }">
                  <div
                    :title="item.name"
                    class="text-[13px] leading-[16px] text-[#1C1C1E] overflow-hidden whitespace-nowrap text-ellipsis"
                  >
                    {{ item.name }}
                  </div>
                  <div
                    :title="transferLangByWords(item.desc, needTransferWords)"
                    class="text-[12px] leading-[16px] text-[#6E6E73] mt-[2px] overflow-hidden whitespace-nowrap text-ellipsis"
                    v-if="item?.desc"
                  >
                    {{ transferLangByWords(item.desc, needTransferWords) }}
                  </div>
                </slot>
              </div>
            </div>
            <div class="flex items-center text-[16px] leading-[20px]">
              <slot name="value" :row="item">{{ item.value }}</slot>
            </div>
          </div>
        </template>
        <div
          v-else
          class="flex items-center justify-center rounded-[12px] h-full"
          style="background: rgba(28, 28, 30, 0.06)"
        >
          <DmpEmpty />
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<style lang="scss" scoped></style>
