/*
 * @Date: 2025-07-09 17:14:50
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-29 11:46:31
 * @FilePath: /MyBusinessV2/packages/components/src/barStackChart/props.ts
 */
export interface BarStackSeries {
  name: string;
  data: number[];
  color?: string;
}
export interface BarStackChartProps {
  title?: string;
  subtitle?: string;
  xAxisData: string[];
  series: BarStackSeries[];
  legendNames?: string[];
  loading?: boolean;
  heightClass?: string;
  emptyText?: string;
  firstRender?: boolean;
  minInterval?: number; // Y轴最小间隔
}
