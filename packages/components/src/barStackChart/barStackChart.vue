<script setup lang="ts">
import { computed, Ref, inject } from "vue";
import * as echarts from "echarts";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { Bar<PERSON><PERSON> } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { BarStackChartProps } from "@/barStackChart/props";
import { DmpEmpty } from "../empty";
import { TransferLang } from "../../../common/types";
import { echartIsTopLayer, echartSetYaxisMax } from "@/utils";

const tt = inject<Ref<TransferLang>>("tt");

use([CanvasRenderer, BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent]);
defineOptions({ name: "DmpBarStackChart" });
const props = defineProps<BarStackChartProps>();

const colorList = [
  "#FF5B6A", // 非常严重
  "#FFA940", // 严重
  "#59ADC4", // 中等
];

const yOptions = computed(() => {
  const values = props.series.reduce((acc, curr) => {
    curr.data.forEach((value, index) => {
      acc[index] += value;
    });
    return acc;
  }, new Array(props.series[0].data.length).fill(0));

  return echartSetYaxisMax(values);
});

const finalOption = computed(() => ({
  animation: props.firstRender,
  animationDuration: props.firstRender ? 800 : 0,
  animationEasing: (props.firstRender ? "cubicOut" : "linear") as any,
  grid: {
    left: 8,
    right: 8,
    top: 8,
    bottom: 8,
    containLabel: true,
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 8,
    textStyle: { color: "#6E6E73", fontSize: 11, lineHeight: 16 },
    formatter: (params) => {
      const xLabel = params[0]?.name ?? params[0]?.axisValue ?? "";
      const rows = params
        .map((item) => {
          let value = item.data?.value ?? item.data;
          if (value === null || value === undefined || value === "") {
            value = "-";
          }
          return `
        <div style="display: flex; justify-content: space-between; align-items: center; margin: 2px 0;">
          <div style="display: flex; align-items: center;">
            <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${item.color};margin-right:4px;"></span>
            ${item.seriesName}
          </div>
          <div style="margin: 0 20px 0 8px;">${value}</div>
        </div>
      `;
        })
        .join("");
      return `<div style="font-weight: 500; margin-bottom: 4px;">${xLabel}</div>${rows}`;
    },
  },
  xAxis: {
    type: "category",
    boundaryGap: true,
    data: props.xAxisData,
    axisLine: {
      show: true,
      lineStyle: {
        color: "#E4E5E7",
        width: 3,
        type: "solid",
      },
      z: 10,
    },
    axisTick: {
      show: true,
      alignWithLabel: true,
      interval: 0,
      length: 8,
      lineStyle: {
        color: "#E4E5E7",
        width: 2,
        cap: "round",
      },
    },
    axisLabel: {
      color: "rgba(28,28,30,0.4)",
      fontSize: 11,
      fontWeight: 400,
      margin: 10,
      align: "center",
      padding: [0, 0, 0, 0],
    },
    splitLine: { show: false },
  },
  yAxis: {
    type: "value",
    min: 0,
    minInterval: props.minInterval ?? 1,
    ...yOptions.value,
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      color: "rgba(28,28,30,0.4)",
      fontSize: 11,
      margin: 2,
      align: "right",
      padding: [0, 4, 0, 0],
      formatter: function (val: number) {
        return val.toLocaleString();
      },
    },
    splitLine: {
      show: true,
      showMinLine: false,
      lineStyle: {
        color: "rgba(28,28,30,0.06)",
        type: "dashed",
        width: 1,
      },
    },
  },
  series: props.series.map((s, i) => ({
    name: tt.value(props.legendNames?.[i] || s.name),
    type: "bar",
    stack: "total",
    data: s.data.map((value, dataIndex) => {
      const isTopLayer = echartIsTopLayer(value, dataIndex, i, props);
      return {
        value,
        itemStyle: {
          animationDelay: props.firstRender ? i * 200 + dataIndex * 50 : 0,
          ...(isTopLayer && { borderRadius: [1, 1, 0, 0] }),
        },
      };
    }),
    barWidth: 8,
    animationDelay: props.firstRender ? i * 200 : 0,
    animationDuration: props.firstRender ? 600 : 0,
    animationEasing: (props.firstRender ? "cubicOut" : "linear") as any,
    itemStyle: {
      color: s.color || colorList[i % colorList.length],
    },
    emphasis: {
      focus: "series",
      blurScope: "coordinateSystem",
      itemStyle: {
        opacity: 1,
      },
    },
    blur: {
      itemStyle: {
        opacity: 0.32,
      },
    },
  })),
}));

const heightClass = computed(() => {
  return props.heightClass ?? "h-[155px]";
});
</script>

<template>
  <div>
    <div v-if="props.title" class="text-[14px] font-medium text-[#1C1C1E] leading-[20px] mb-[8px]">
      {{ tt(props.title) }}
    </div>
    <div class="flex items-center justify-between mb-[12px]">
      <div v-if="props.subtitle" class="text-[12px] font-regular text-[#1C1C1E] leading-[16px]">
        {{ tt(props.subtitle) }}
      </div>
      <div class="flex items-center gap-[8px]">
        <div v-for="(name, i) in props.legendNames || props.series.map((s) => s.name)" :key="name">
          <span
            class="inline-block w-[8px] h-[8px] rounded-full mr-[4px]"
            :style="{
              background: props.series[i]?.color || colorList[i % colorList.length],
            }"
          ></span>
          <span class="text-[11px] font-regular text-[#6E6E73] leading-[16px]">{{ tt(name) }}</span>
        </div>
      </div>
    </div>
    <el-skeleton animated :class="heightClass" :loading="props.loading">
      <template #template>
        <el-skeleton-item variant="rect" class="h-full rounded-[12px] card-skeleton" />
      </template>
      <template #default>
        <template v-if="props.series.length">
          <v-chart
            :option="finalOption"
            autoresize
            class="w-full"
            :class="heightClass"
            :key="`${props.title}-${props.subtitle}-${JSON.stringify(props.xAxisData)}-${JSON.stringify(props.series)}`"
          />
        </template>
        <div
          v-else
          class="flex items-center justify-center rounded-[12px]"
          :class="heightClass"
          style="background: rgba(28, 28, 30, 0.06)"
        >
          <DmpEmpty :text="props.emptyText" />
        </div>
      </template>
    </el-skeleton>
  </div>
</template>
