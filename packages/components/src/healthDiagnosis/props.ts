export interface HealthDiagnosisProps {
  /** 组件ID，用于弹窗匹配 */
  id?: string;
  /** 健康度分数 0-100 */
  progress?: number;
  /** 状态文本 */
  statusText?: string;
  /** 打开方向 */
  openDirection?: "top" | "bottom" | "left" | "right";
  /** 指示器大小 */
  size?: number;
  /** 线条宽度 */
  strokeWidth?: number;
  /** 进度条颜色 */
  strokeColor?: string[];
  /** 健康提示列表 */
  healthTips?: string[];
  /** 提示轮播间隔时间(ms) */
  interval?: number;
  /** 是否循环播放 */
  loop?: boolean;
  /** 是否自动播放 */
  autoplay?: boolean;
  /** 警报状态项列表 */
  alertItems?: AlertStatusItem[];
  /** 是否为移动端 */
  isMobile?: boolean;
  /** 标题 */
  title?: string;
  /** 图标 */
  icon?: string;
  /** 健康度等级 */
  level?: string;
  emptyText?: string;
  updateTime?: string;
}

export interface AlertStatusItem {
  /** 弹窗 id */
  id: string;
  /** 标题 */
  title: string;
  /** 进度值 0-100 */
  progress: number;
  /** 颜色 - 必需属性 */
  color: string;
  /** 状态文本 */
  statusText: string;
  /** 渐变色数组 */
  gradient?: string[];
  /** 状态级别 */
  statusLevel?: "normal" | "warning" | "good" | "qualified";
  /** 是否显示文字 */
  showText?: boolean;
  /** 进度单位 */
  progressUnit?: string;
  /** 警告数量 */
  warningCnt?: number | string;
  /** 文字颜色 */
  textColor?: string;
}
