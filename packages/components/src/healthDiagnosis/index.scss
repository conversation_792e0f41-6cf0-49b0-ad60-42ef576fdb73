.dmp-health-diagnosis {
  width: 100%;
  overflow: hidden;
  .health-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .title-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: #1c1c1e;
      margin: 0;
    }
  }
  .health-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .progress-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 152px;
    justify-content: space-between;
  }

  .alert-section {
    padding: 8px 0;
    height: 312px;
    padding-right: 4px;
  }

  .health-score-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
  }

  .score-number {
    font-size: 28px;
    font-weight: bold;
    color: #2ebb72;
    line-height: 1;
  }

  .score-label {
    font-size: 12px;
    color: #666;
    line-height: 1;
  }

  .health-status {
    font-size: 14px;
    font-weight: 500;
    color: #2ebb72;
  }

  .health-tips {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .tip-card {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.4;
    border-left: 3px solid;

    &.warning {
      background-color: #fff3e0;
      border-left-color: #ff9800;
      color: #e65100;
    }

    &.success {
      background-color: #e8f5e8;
      border-left-color: #4caf50;
      color: #2e7d32;
    }
  }

  .progress-bar {
    position: relative;
    overflow: visible;
    flex-shrink: 0;
    height: 100%;

    :deep(.el-progress-bar__outer) {
      background: #dadee0;
    }

    :deep(.el-progress-bar__inner) {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 0px;
        width: 8px;
        height: 8px;
        background: rgba(28, 28, 30, 0.4);
        border-radius: 50%;
        transform: translateY(-50%);
        border: 2px solid #ebebeb;
      }
    }

    :deep(.el-progress) {
      width: 100%;
    }
  }

  .dmp-health-diagnosis-alert {
    .content {
      display: flex;
      flex-direction: column;
    }
  }
}
