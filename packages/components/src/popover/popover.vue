<!--
 * @Date: 2025-07-09 17:14:25
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-15 19:34:54
 * @FilePath: /MyBusinessV2/packages/components/src/popover/popover.vue
-->
<script setup lang="ts">
/**
 * 通用弹窗浮层组件，基于 Element Plus el-popover 封装。
 * 支持 placement、width、trigger、popperClass、teleported 等参数。
 * reference 和内容均用 slot，便于复用。
 */
import { defineProps, withDefaults, defineEmits } from "vue";
import { popoverProps } from "@/popover/props.ts";
import { Icon } from "../icon/icon";

defineOptions({ name: "DmpPopover" });
const props = withDefaults(defineProps<popoverProps>(), {
  title: "健康诊断",
  placement: "right",
  width: 360,
  trigger: "hover",
  popperClass: "dmp-popover",
  teleported: true,
  extraIcon: "download",
  extraTitle: "下载明细",
  visible: false,
});

const emit = defineEmits(["extra-click"]);
function handleExtraClick(e: MouseEvent) {
  emit("extra-click", e);
}
</script>
<template>
  <el-popover
    :trigger="trigger"
    :placement="placement"
    :width="width"
    :show-arrow="true"
    :popper-class="popperClass"
    :teleported="teleported"
    :visible
    :popper-options="{
      modifiers: [
        {
          name: 'preventOverflow',
          options: {
            padding: 16,
          },
        },
      ],
    }"
  >
    <template #reference>
      <slot name="reference" />
    </template>
    <slot />
  </el-popover>
</template>
