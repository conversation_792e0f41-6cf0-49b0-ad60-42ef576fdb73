@media (max-width: 768px) {
  .dmp-popover {
    position: fixed !important;
    width: 100% !important;
    inset: auto auto 0 0 !important;
    border-radius: 24px 24px 0 0 !important;
    overflow-y: auto !important;
    overflow-x: hidden;
    overscroll-behavior: contain !important; // 防止滚动穿透到页面
    -webkit-overflow-scrolling: touch !important; // iOS 平滑滚动
  }
  .dmp-popover.el-popper {
    background: rgba(255, 255, 255, 0.88) !important;
  }
}

@media (min-width: 768px) {
  .dmp-popover {
    border-radius: 24px;
  }
}

.dmp-popover.el-popper {
  border-radius: 24px;
  padding: 0;
  background: rgba(255, 255, 255, 0.64);
  box-shadow:
    0 0 8px rgba($color: #000000, $alpha: 0.08),
    0 16px 32px 0 rgba(0, 0, 0, 0.16) !important;
  backdrop-filter: blur(16px) !important;
  border: none;

  &[data-popper-placement="right"] {
    .el-popper__arrow::before {
      background: rgba(255, 255, 255, 0.8) !important;
      clip-path: polygon(0 0, 0 100%, 100% 100%);
    }
  }
  &[data-popper-placement="left"] {
    .el-popper__arrow::before {
      background: rgba(255, 255, 255, 0.8) !important;
      clip-path: polygon(0 0, 100% 0, 100% 100%);
    }
  }
}
.popover-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  .popover-header-title {
    font-size: 16px;
    font-weight: 600;
    color: #1c1c1e;
  }
  .popover-header-extra {
    background: rgba(0, 113, 227, 0.2);
    cursor: pointer;
    color: #0071e3;
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 16px;
  }
}
