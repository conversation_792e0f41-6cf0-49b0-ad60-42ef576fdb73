/*
 * @Date: 2025-07-09 17:14:50
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-10 14:37:59
 * @FilePath: /MyBusinessV2/packages/components/src/popover/props.ts
 */
export interface popoverProps {
  title?: string;
  placement?: "top" | "bottom" | "left" | "right";
  width?: string | number;
  trigger?: "hover" | "click" | "focus" | "contextmenu";
  popperClass?: string;
  teleported?: boolean;
  extraIcon?: string;
  extraTitle?: string;
  visible?: boolean;
}
