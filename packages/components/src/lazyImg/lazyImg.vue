<script setup lang="ts">
import { ref, computed } from "vue";
import { LazyImgProps } from "./props";
import defaultPlaceholder from "./assets/placeholder-pic.png";

const props = withDefaults(defineProps<LazyImgProps>(), {
  alt: "",
  class: "",
});

const loaded = ref(false);
const imageRef = ref<HTMLImageElement>();

// 占位图地址
const placeholderSrc = computed(() => props.placeholderSrc || defaultPlaceholder);

// 图片加载成功
const onLoad = () => {
  loaded.value = true;
};

// 图片加载失败,依旧使用占位图
const onError = () => {
  loaded.value = false;
};
</script>

<template>
  <div :class="['lazy-img-container', props.class]">
    <div v-show="!loaded" class="lazy-img-placeholder">
      <img :src="placeholderSrc" :alt="props.alt" />
    </div>

    <img
      v-show="loaded"
      ref="imageRef"
      :src="props.src"
      :alt="props.alt"
      :class="['lazy-img-real', { 'fade-in': loaded }]"
      @load="onLoad"
      @error="onError"
    />
  </div>
</template>
