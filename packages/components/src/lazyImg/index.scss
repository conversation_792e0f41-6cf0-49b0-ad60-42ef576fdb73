.lazy-img-container {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 100%;
  height: 100%;

  .lazy-img-real {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &.fade-in {
      opacity: 1;
    }
  }

  .lazy-img-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(28, 28, 30, 0.06);

    img {
      width: 42%;
      height: auto;
      opacity: 0.4;
    }
  }
}
