# LazyImg 懒加载图片组件

一个支持懒加载和占位图的简洁图片组件。

## 特性

- ✅ 懒加载图片
- ✅ 自定义占位图片（可选，不传则使用默认占位图）
- ✅ 渐入动画效果
- ✅ 简洁的两种状态：有图片/无图片
- ✅ 完全响应式设计

## 使用方法

### 基础用法

```vue
<template>
  <DmpLazyImg src="https://example.com/image.jpg" alt="示例图片" />
</template>
```

### 自定义占位图

```vue
<template>
  <DmpLazyImg
    src="https://example.com/image.jpg"
    placeholder-src="https://example.com/placeholder.jpg"
    alt="示例图片"
  />
</template>
```

### 自定义样式

```vue
<template>
  <div class="image-wrapper">
    <DmpLazyImg src="https://example.com/image.jpg" class="custom-image" alt="示例图片" />
  </div>
</template>

<style>
.image-wrapper {
  width: 300px;
  height: 200px;
}

.custom-image {
  border-radius: 8px;
}
</style>
```

## API

### Props

| 参数           | 类型     | 默认值 | 说明                               |
| -------------- | -------- | ------ | ---------------------------------- |
| src            | `string` | -      | 图片源地址（必需）                 |
| placeholderSrc | `string` | -      | 占位图片地址，不传则使用默认占位图 |
| alt            | `string` | `""`   | 图片 alt 属性                      |
| class          | `string` | `""`   | 自定义 class                       |

### 行为说明

1. **加载流程**：
   - 初始显示占位图片（50%宽度，居中显示）
   - 后台加载真实图片
   - 加载成功后渐入显示真实图片

2. **状态简化**：
   - **无图片状态**：初始加载或加载失败时显示占位图
   - **有图片状态**：真实图片加载成功后显示

3. **默认占位图**：
   - 不传 `placeholderSrc` 时使用内置的默认占位图
   - 默认占位图位于 `assets/placeholder-pic.png`
   - 占位图以50%宽度居中显示，带有0.7透明度
