:deep(.dmp-flex-card) {
  padding-inline: 8px;
  padding-bottom: 8px;

  .title {
    padding-inline: 8px;
  }

  .content {
    flex: 1;

    ::-webkit-scrollbar {
      display: none;
    }
  }
}

@mixin vertical-line {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 1px;
  height: 48px;
  background: rgba(28, 28, 30, 0.1);
}

.grid-card {
  .el-tooltip__trigger {
    position: relative;
    &:not(:first-child)::before {
      @include vertical-line();
    }
  }

  .screen-item + .screen-item {
    &::before {
      @include vertical-line();
    }
  }
}
