<script setup lang="ts">
import DmpFlexCard from "@/flexCard/flexCard.vue";
import DmpValueWithUnit from "@/valueWithUnit/ValueWithUnit.vue";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";
import DmpEmpty from "@/empty/empty.vue";
import { computed, toRefs, withDefaults, inject, Ref } from "vue";
import { DmpDataCenterCard2x1Emits, DmpDataCenterCard2x1Props } from "./props.ts";
import { TransferLang } from "../../../common/types";
import noteIconUrl from "@assets/icons/note.svg?url";
import DmpScrollSnap from "@/scrollSnap/scrollSnap.vue";

const tt = inject<Ref<TransferLang>>("tt");

defineOptions({ name: "DmpDataCenterCard2x1" });
const emit = defineEmits<DmpDataCenterCard2x1Emits>();
const props = withDefaults(defineProps<DmpDataCenterCard2x1Props>(), {
  id: "",
  isMobile: false,
  showArrow: false,
  countClass: () => [],
  unitClass: () => [],
  list: () => [],
  useToolTipUpdateTime: false,
});
const {
  isMobile,
  updateTime,
  list,
  title,
  showArrow,
  countClass,
  unitClass,
  useToolTipUpdateTime,
} = toRefs(props);

// 容器布局占位
const gridSpan = computed(() => {
  const colMap = {
    1: 1,
    2: 2,
    3: 2,
  };
  return {
    row: 1,
    col: list.value.length >= 4 ? (isMobile.value ? 2 : 4) : (colMap?.[list.value.length] ?? 1),
  };
});

const selfCountClass = computed(() => {
  if (list.value.length === 1) {
    return ["text-[18px]", "leading-[18px]"];
  }
  return ["text-[16px]", "leading-[16px]"];
});

const showIcon = computed(() => {
  return list.value.length > 1;
});

// 每屏显示数量
const itemsPerScreen = computed(() => {
  if (isMobile.value) return 2;
  return list.value.length < 4 ? 2 : 4;
});
</script>

<template>
  <DmpFlexCard :title="tt(title)" :grid-span="gridSpan" :show-arrow="showArrow">
    <template #titleExtra>
      <el-tooltip effect="dark" :content="updateTime" placement="top" v-if="useToolTipUpdateTime">
        <img
          :src="noteIconUrl"
          alt="note"
          class="w-[16px] h-[16px] cursor-pointer opacity-40 hover:opacity-100 transition-all duration-150 block"
        />
      </el-tooltip>
    </template>

    <div class="flex flex-col h-full relative grid-card">
      <template v-if="list.length">
        <DmpScrollSnap :list="list" :items-per-screen="itemsPerScreen">
          <template #default="{ item }">
            <DmpPopoverTrigger v-if="list.length === 1" :id="`${id}.${item.name}`">
              <template v-slot:default="{ active }">
                <div
                  class="flex flex-col relative rounded-[12px] highlightable"
                  :class="{ 'active-item': active }"
                >
                  <div
                    class="flex flex-col gap-[2px] p-[8px]"
                    v-for="countUnitItem in item.countUnitList"
                    :key="countUnitItem.extra"
                  >
                    <div
                      class="text-[13px] leading-[16px] whitespace-nowrap text-[rgba(28,28,30,.64)]"
                    >
                      {{ tt(countUnitItem.extra) }}
                    </div>
                    <DmpValueWithUnit
                      :value="countUnitItem.count"
                      :unit="tt(countUnitItem.unit)"
                      :value-class="[...selfCountClass, ...countClass]"
                      :unit-class="unitClass"
                    />
                  </div>
                  <div
                    class="absolute top-[50%] left-[8px] right-[8px] h-[1px] bg-[rgba(0,0,0,0.04)] group-line"
                  ></div>
                </div>
              </template>
            </DmpPopoverTrigger>

            <DmpPopoverTrigger v-else :id="`${id}.${item.name}`">
              <template v-slot:default="{ active }">
                <div
                  class="flex flex-col gap-[8px] relative p-[8px] rounded-[12px] highlightable h-[80px]"
                  :class="{ 'active-item': active }"
                >
                  <div
                    class="flex items-center justify-center gap-[2px] text-[13px] leading-[20px] text-[#1C1C1E]"
                  >
                    <div v-if="showIcon" class="w-[16px] h-[16px]">
                      <img
                        v-if="item.icon"
                        :src="item.icon"
                        class="w-full h-full"
                        alt=""
                        srcset=""
                      />
                    </div>
                    {{ item.name }}
                  </div>
                  <div class="grid grid-cols-2 gap-[4px]">
                    <div
                      class="flex flex-col gap-[4px] items-center"
                      v-for="countUnitItem in item.countUnitList"
                      :key="countUnitItem.extra"
                    >
                      <DmpValueWithUnit
                        :value="countUnitItem.count"
                        :unit="tt(countUnitItem.unit)"
                        :value-class="[...selfCountClass, ...countClass]"
                        :unit-class="unitClass"
                      />
                      <div
                        class="text-[13px] leading-[16px] whitespace-nowrap text-[rgba(28,28,30,.64)]"
                      >
                        {{ tt(countUnitItem.extra) }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </DmpPopoverTrigger>
          </template>
        </DmpScrollSnap>

        <!-- 更新时间 -->
        <div
          v-if="!useToolTipUpdateTime"
          class="mt-auto py-[4px] px-[8px] text-[11px] leading-[16px] text-[rgba(28,28,30,.4)]"
        >
          {{ updateTime }}
        </div>
      </template>
      <DmpEmpty v-else :text="props.emptyText" />
    </div>
  </DmpFlexCard>
</template>
