<script setup lang="ts">
import { Icon } from "../icon/icon";
import { ItemWithArrowProps } from "./props";

defineOptions({
  name: "DmpItemWithArrow",
});

const props = withDefaults(defineProps<ItemWithArrowProps>(), {
  hideArrow: false,
});

const { hideArrow } = props;
</script>

<template>
  <div class="flex items-center rounded-[12px] justify-between w-full">
    <slot />
    <Icon v-if="!hideArrow" name="arrow-right" />
  </div>
</template>
