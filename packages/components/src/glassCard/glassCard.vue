<script setup lang="ts">
import { computed, ref, inject, Ref } from "vue";
import { GlassCardProps } from "@/glassCard/props.ts";
import { Icon } from "../icon/icon";
import DmpAutoScroller from "../autoScroller/autoScroller.vue";
import { ElSkeleton, ElEmpty, ElSkeletonItem } from "element-plus";
import { TransferLang } from "../../../common/types";
import DmpEmpty from "@/empty/empty.vue";

const tt = inject<Ref<TransferLang>>("tt");

defineOptions({ name: "DmpGlassCard" });

const {
  title,
  icon,
  loading,
  isMobile,
  gridTemplate,
  gridSize,
  contentSize,
  disableScrolling,
  emptyText = "暂无数据",
} = defineProps<GlassCardProps>();

const autoScroller = ref<InstanceType<typeof DmpAutoScroller>>();

const scrollByPage = computed(() => autoScroller.value?.scrollByPage);
</script>

<template>
  <div class="relative flex flex-col" :class="{ 'dmp-glass-card': !isMobile }">
    <div v-if="title" class="flex items-center mb-[12px] gap-[4px] ml-[8px]">
      <Icon v-if="icon" :name="icon" />
      <h3 class="text-[16px] font-medium leading-[24px]">{{ tt(title) }}</h3>
    </div>
    <el-skeleton animated class="h-full opacity-60" :loading>
      <template #template>
        <div class="flex flex-row h-full gap-[8px]">
          <el-skeleton-item
            v-for="item in gridTemplate.col"
            variant="rect"
            :class="`w-[calc(${Math.floor(100 / gridTemplate.col)}%-4px)] h-full min-h-[152px] rounded-[16px]`"
          />
        </div>
      </template>
      <template #default>
        <dmp-auto-scroller
          ref="autoScroller"
          class="grow"
          :disabled="disableScrolling"
          :width="contentSize.width"
          :height="contentSize.height"
        >
          <!-- <template v-if="!isMobile" #indicator="{ atStart, atEnd, scrollable }">
            <transition name="fade">
              <div
                v-if="scrollable && scrollByPage"
                class="absolute top-[20px] right-[24px] flex gap-[2px] z-10"
              >
                <button
                  @click="() => scrollByPage(-1)"
                  :disabled="atStart"
                  class="w-[24px] h-[24px] flex items-center justify-center rounded-full bg-white/70 hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed shadow"
                  aria-label="向左滚动"
                >
                  <svg
                    class="w-[12px] h-[12px]"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  @click="() => scrollByPage(1)"
                  :disabled="atEnd"
                  class="w-[24px] h-[24px] flex items-center justify-center rounded-full bg-white/70 hover:bg-white/90 disabled:opacity-50 disabled:cursor-not-allowed shadow"
                  aria-label="向右滚动"
                >
                  <svg
                    class="w-[12px] h-[12px]"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </transition>
          </template> -->
          <template #default>
            <div
              class="grid gap-[8px] grid-flow-row"
              :style="{
                gridTemplateColumns: `repeat(${gridTemplate.col}, 1fr)`,
                'grid-template-rows': `repeat(${gridTemplate.row || 1},${gridSize.row}px)`,
                'grid-auto-rows': `${gridSize.row}px`,
              }"
            >
              <slot>
                <DmpEmpty class="absolute-center">
                  <template #text>
                    <p class="text-[13px] text-normal">
                      {{ tt(emptyText) }}
                    </p>
                  </template>
                </DmpEmpty>
              </slot>
            </div>
          </template>
        </dmp-auto-scroller>
      </template>
    </el-skeleton>
  </div>
</template>
