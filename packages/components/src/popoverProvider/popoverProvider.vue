<script setup lang="ts">
import { PopoverProviderProps } from "./props.ts";
import { provide, defineProps, toRef } from "vue";

defineOptions({ name: "DmpPopoverProvider" });
const { schema, compsRegistry, isMobile } = defineProps<PopoverProviderProps>();

provide(
  "schema",
  toRef(() => schema)
);
provide("compsRegistry", compsRegistry);
provide("isMobile", isMobile);
</script>
<template>
  <slot></slot>
</template>
