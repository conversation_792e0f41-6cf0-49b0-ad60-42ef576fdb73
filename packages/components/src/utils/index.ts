export function dfs<T extends { children?: T[] }>(node: T, cb: (n: T) => void) {
  cb(node);
  node.children && node.children.forEach((child) => dfs(child, cb));
}

export function comparePaths(path1: string, path2: string) {
  const path1List = path1.split(".");
  const path2List = path2.split(".");

  for (let i = 0; i < Math.min(path1List.length, path2List.length); i++) {
    const item1 = path1List[i];
    const item2 = path2List[i];
    if (item1 !== "*" && item2 !== "*" && item1 !== item2) {
      return false;
    }
  }

  return true;
}

export function getMatches(path1: string, path2: string) {
  const matches: string[] = [];

  const path1List = path1.split(".");
  const path2List = path2.split(".");

  for (let i = 0; i < Math.min(path1List.length, path2List.length); i++) {
    const item1 = path1List[i];
    const item2 = path2List[i];
    if (item1 === "*") {
      matches.push(item2);
    } else if (item2 === "*") {
      matches.push(item1)
    }
  }

  return matches;
}

export function replaceMatches(source: string, matches: string[]) {
  if (!source) return source;
  for (let i = 0; i < matches.length; i++) {
    const regExp = new RegExp(`\\$${i + 1}`, "g");
    source = source.replace(regExp, matches[i]);
  }
  return source;
}

// interface TransforWords {
//   [key: string]: string;
// }
// 使用packages/common/hooks/languageReplace.ts
// 替换字符串中的想要多语言的词语 ('你好123世界456', { '你好': 'hello', 世界: 'world'})  => 'hello123world456'
// export function transferLangByWords(str: string, transforWords: TransforWords): string {
//   if (!str) return "";
//   let replacedStr = str;
//   Object.entries(transforWords).forEach((word) => {
//     replacedStr = replacedStr.replace(new RegExp(word[0], "g"), word[1]);
//   });
//   return replacedStr;
// }

// 柱状图判断当前数据点是否是这个位置的最顶层
export const echartIsTopLayer = (value: number, dataIndex: number, i: number, props: any) => {
  for (let j = i + 1; j < props.series.length; j++) {
    const higherValue = props.series[j].data[dataIndex];
    if (higherValue !== null && higherValue !== undefined && higherValue > 0) {
      return false;
    }
  }
  return value !== null && value !== undefined && value > 0;
};

// 把一个数变成 1/2/5/10 中最接近又比它大（或小）的整齐值,最后还原成原始量级
const number2nice = (v: number): number => {
  const exp = Math.floor(Math.log10(v));
  const f = v / Math.pow(10, exp);
  return (f < 1.5 ? 1 : f < 3 ? 2 : f < 7 ? 5 : 10) * Math.pow(10, exp);
};

// 计算y轴最大值，方法来源自echarts
export const echartSetYaxisMax = (values: number[]): { max?: number; interval?: number } => {
  const dataMax = Math.max(...values);
  // 预估一个间隔
  const roughInterval = Math.ceil(dataMax / 5);
  // 获取“漂亮”的整数
  const interval = number2nice(roughInterval);
  const max = Math.ceil((dataMax + interval) / interval) * interval;
  return interval ? { max, interval } : {};
};
