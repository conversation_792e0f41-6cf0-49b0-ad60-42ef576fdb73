:deep(.dmp-flex-card) {
  padding-inline: 8px;
  padding-bottom: 8px;

  .title {
    padding-inline: 8px;
  }

  .content {
    flex: 1;

    ::-webkit-scrollbar {
      display: none;
    }
  }
}

@mixin vertical-line {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 1px;
  height: 48px;
  background: rgba(28, 28, 30, 0.1);
}

.grid-card {
  .el-tooltip__trigger {
    position: relative;
  }

  .el-tooltip__trigger:has(> .horizontal-line) + .el-tooltip__trigger:has(> .horizontal-line) {
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 8px;
      right: 8px;
      height: 1px;
      width: auto;
      background: rgba(#1c1c1e, 0.08);
    }
  }

  .el-tooltip__trigger:has(> .vertical-line) + .el-tooltip__trigger:has(> .vertical-line) {
    &::before {
      @include vertical-line();
    }
  }

  .screen-item + .screen-item {
    &::before {
      @include vertical-line();
    }
  }
}
