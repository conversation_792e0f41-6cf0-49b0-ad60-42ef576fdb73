export interface DmpDataCenterCard1x1Props {
  id: string;
  title: string;
  updateTime: string;
  showArrow?: boolean;
  countClass?: string[];
  unitClass?: string[];
  list: DmpDataCenterCard1x1ListItem[];
  emptyText?: string;
  useToolTipUpdateTime?: boolean;
}

export interface DmpDataCenterCard1x1ListItem {
  name: string;
  count: number | string;
  unit: string;
  icon: string;
}

export type DmpDataCenterCard1x1Emits = {
  click: [item: DmpDataCenterCard1x1ListItem];
};

export interface DmpDataCenterRetailWithOriginalRes {
  list: DmpDataCenterCard1x1ListItem[];
  updateTime: string;
}
