<script setup lang="ts">
import DmpFlexCard from "@/flexCard/flexCard.vue";
import DmpValueWithUnit from "@/valueWithUnit/ValueWithUnit.vue";
import DmpPopoverTrigger from "@/popoverTrigger/popoverTrigger.vue";
import DmpEmpty from "@/empty/empty.vue";
import { computed, toRefs, withDefaults, inject, Ref } from "vue";
import { DmpDataCenterCard1x1Props } from "./props.ts";
import { TransferLang } from "../../../common/types";
import noteIconUrl from "@assets/icons/note.svg?url";
import DmpScrollSnap from "@/scrollSnap/scrollSnap.vue";

const tt = inject<Ref<TransferLang>>("tt");

defineOptions({ name: "DmpDataCenterCard1x1" });
const props = withDefaults(defineProps<DmpDataCenterCard1x1Props>(), {
  id: "",
  showArrow: false,
  countClass: () => [],
  unitClass: () => [],
  list: () => [],
  emptyText: "No Data",
  useToolTipUpdateTime: false,
});
const { id, updateTime, list, title, showArrow, countClass, unitClass, useToolTipUpdateTime } =
  toRefs(props);

// 容器布局占位
const gridSpan = computed(() => {
  return { row: 1, col: list.value.length > 2 ? 2 : 1 };
});

const itemClass = computed(() => {
  if (list.value.length > 2) {
    return ["gap-[8px]", "items-center", "vertical-line", "h-[80px]"];
  }
  return ["gap-[2px]", list.value.length === 2 ? "horizontal-line" : ""];
});

const selfCountClass = computed(() => {
  switch (list.value.length) {
    case 1:
      return ["text-[24px]", "leading-[24px]"];
    case 2:
      return ["text-[18px]", "leading-[18px]"];
    default:
      return ["text-[16px]", "leading-[16px]"];
  }
});

const showIcon = computed(() => {
  return list.value.length > 2;
});

// 每屏显示数量
const itemsPerScreen = computed(() => {
  return list.value.length >= 4 ? 4 : list.value.length;
});
</script>

<template>
  <DmpFlexCard :title="tt(title)" :grid-span="gridSpan" :show-arrow="showArrow">
    <template #titleExtra>
      <el-tooltip effect="dark" :content="updateTime" placement="top" v-if="useToolTipUpdateTime">
        <img
          :src="noteIconUrl"
          alt="note"
          class="w-[16px] h-[16px] cursor-pointer opacity-40 hover:opacity-100 transition-all duration-150 block"
        />
      </el-tooltip>
    </template>

    <div class="flex flex-col h-full relative grid-card">
      <template v-if="list.length">
        <DmpScrollSnap
          :list="list"
          :items-per-screen="itemsPerScreen"
          :need-calc-column="list.length > 2"
        >
          <template #default="{ item }">
            <DmpPopoverTrigger :id="`${id}.${item.name}`">
              <template v-slot:default="{ active }">
                <div
                  class="flex flex-col relative p-[8px] rounded-[12px] highlightable"
                  :class="[itemClass, { 'active-item': active }]"
                >
                  <div v-if="showIcon" class="w-[20px] h-[20px]">
                    <img v-if="item.icon" :src="item.icon" class="w-full h-full" alt="" srcset="" />
                  </div>
                  <div
                    class="flex flex-col justify-center"
                    :class="[
                      { 'flex-col-reverse gap-[2px]': list.length <= 2 },
                      { 'items-center gap-[4px]': list.length > 2 },
                    ]"
                  >
                    <DmpValueWithUnit
                      :value="item.count"
                      :unit="tt(item.unit)"
                      :value-class="[...selfCountClass, ...countClass]"
                      :unit-class="unitClass"
                    />
                    <div
                      class="text-[13px] leading-[16px] whitespace-nowrap text-[rgba(28,28,30,.64)]"
                    >
                      {{ tt(item.name || "") }}
                    </div>
                  </div>
                </div>
              </template>
            </DmpPopoverTrigger>
          </template>
        </DmpScrollSnap>

        <!-- 更新时间 -->
        <div
          v-if="!useToolTipUpdateTime"
          class="mt-auto px-[8px] py-[4px] text-[11px] leading-[16px] text-[rgba(28,28,30,.4)]"
        >
          {{ updateTime }}
        </div>
      </template>
      <DmpEmpty v-else :text="props.emptyText" />
    </div>
  </DmpFlexCard>
</template>
