/*
 * @Date: 2025-07-09 17:14:50
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-29 12:01:39
 * @FilePath: /MyBusinessV2/packages/components/src/lineChart/props.ts
 */
export interface LineChartSeriesProps {
  name: string;
  data: (number | string)[];
  color?: string;
  [key: string]: any;
}
export interface LineChartProps {
  title?: string;
  subtitle?: string;
  xAxisData: (number | string)[];
  series: LineChartSeriesProps[];
  yMax?: number;
  yMin?: number;
  yInterval?: number;
  loading?: boolean;
  heightClass?: string;
  firstRender?: boolean;
  minInterval?: number; // Y轴最小间隔
}
