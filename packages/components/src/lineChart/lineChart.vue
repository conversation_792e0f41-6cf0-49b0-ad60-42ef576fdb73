<script setup lang="ts">
import { computed, ref } from "vue";
import * as echarts from "echarts";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { LineChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import { LineChartProps } from "@/lineChart/props";
import { DmpEmpty } from "../empty";
import { TransferLang } from "../../../common/types";
import { inject, Ref } from "vue";
import { echartSetYaxisMax } from "@/utils";

const tt = inject<Ref<TransferLang>>("tt");

use([CanvasRenderer, LineChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent]);
defineOptions({ name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" });

const props = defineProps<LineChartProps>();

const yOptions = computed(() => {
  const values = props.series
    .map((s) => s.data)
    .flat()
    .map((item) => Number(item));
  return echartSetYaxisMax(values);
});

const finalOption = computed(() => ({
  animation: props.firstRender,
  animationDuration: props.firstRender ? 1000 : 0,
  animationEasing: props.firstRender ? "cubicOut" : "linear",
  grid: {
    left: 8,
    right: 8,
    top: 8,
    bottom: 8,
    containLabel: true,
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    backgroundColor: "#FFFFFF",
    borderRadius: 8,
    padding: 8,
    textStyle: { color: "#6E6E73", fontSize: 11, lineHeight: 16 },
    formatter: (params: any[]) => {
      return params
        .map(
          (item) => `
        <div style="display: flex; justify-content: space-between; align-items: center; margin: 2px 0;">
          <div style="display: flex; align-items: center;">
            <span style="display:inline-block;width:8px;height:8px;border-radius:50%;background:${item.color};margin-right:4px;"></span>
           ${item.name || item.axisValue}
          </div>
          <div style="margin: 0 20px 0 8px;">${item.data?.value ?? item.data ?? "-"}</div>
        </div>
      `
        )
        .join("");
    },
  },
  legend: { show: false },
  xAxis: {
    type: "category",
    boundaryGap: false,
    data: props.xAxisData,
    axisLine: {
      show: true,
      lineStyle: {
        color: "#E4E5E7",
        width: 3,
        type: "solid",
      },
      z: 10,
    },
    axisTick: {
      show: true,
      alignWithLabel: true,
      length: 8,
      lineStyle: {
        color: "#E4E5E7",
        width: 2,
        cap: "round",
      },
    },
    axisLabel: {
      color: "rgba(28,28,30,0.4)",
      fontSize: 11,
      fontWeight: 400,
      margin: 10,
      align: "center",
      padding: [0, 0, 0, 0],
    },
    splitLine: { show: false },
  },
  yAxis: {
    type: "value",
    min: props.yMin ?? 0,
    // axisLine: {
    //   show: true,
    //   lineStyle: {
    //     color: "rgba(28,28,30,0.4)",
    //     width: 1,
    //   },
    // },
    minInterval: props.minInterval ?? 1,
    ...yOptions.value,
    axisTick: { show: false },
    axisLabel: {
      color: "rgba(28,28,30,0.4)",
      fontSize: 11,
      margin: 2,
      align: "right",
      padding: [0, 4, 0, 0],
      formatter: function (val: number) {
        return val.toLocaleString();
      },
    },
    splitLine: {
      show: true,
      showMinLine: false,
      interval: function (index: number, value: number) {
        // 不显示 Y=0 位置的网格线
        return value > 0;
      },
      lineStyle: {
        color: "rgba(28,28,30,0.06)",
        type: "dashed",
        width: 1,
      },
    },
  },
  series: props.series.map((s) => ({
    ...s,
    data: s.data.map((v: number) => (v < 0 ? 0 : v)),
    type: "line",
    symbol: "circle",
    symbolSize: 4,
    connectNulls: true,
    itemStyle: {
      color: s.color ?? "#4CB6D8",
      borderColor: "#4CB6D8",
      borderWidth: 3,
      //   shadowColor: "rgba(76,182,216,0.2)",
    },
    lineStyle: {
      color: s.color ?? "#4CB6D8",
      width: 3,
    },
    areaStyle: {
      color: "rgba(87,173,196,0.12)",
    },
    emphasis: {
      focus: "series",
      itemStyle: {
        borderColor: s.color ?? "#4CB6D8",
        borderWidth: 4,
      },
    },
    smooth: false,
  })),
}));

const heightClass = computed(() => {
  return props.heightClass ?? "h-[174px]";
});
</script>

<template>
  <div>
    <div class="text-[14px] font-medium text-[#1C1C1E] leading-[20px] mb-[8px]">
      {{ props.title ? tt(props.title) : "" }}
    </div>
    <div class="mb-[12px] text-[12px] font-regular text-[#1C1C1E] leading-[16px]">
      {{ props.subtitle ? tt(props.subtitle) : "" }}
    </div>
    <el-skeleton animated :class="heightClass" :loading="props.loading">
      <template #template>
        <el-skeleton-item variant="rect" class="h-full rounded-[12px] card-skeleton" />
      </template>
      <template #default>
        <v-chart
          v-if="props.series?.length && props.series[0].data.length > 0"
          :option="finalOption"
          autoresize
          class="w-full"
          :class="heightClass"
          :key="`${props.title}-${props.subtitle}-${JSON.stringify(props.xAxisData)}-${JSON.stringify(props.series)}`"
        />
        <div
          v-else
          class="flex items-center justify-center rounded-[12px]"
          :class="heightClass"
          style="background: rgba(28, 28, 30, 0.06)"
        >
          <DmpEmpty />
        </div>
      </template>
    </el-skeleton>
  </div>
</template>
