<!--
 * @Date: 2025-07-11 16:56:26
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-28 16:30:03
 * @FilePath: /MyBusinessV2/packages/components/src/infoDetailTable/infoDetailTable.vue
-->
<template>
  <div>
    <div v-if="title" class="mb-[8px] text-[14px] font-medium text-[#1C1C1E] leading-[20px]">
      {{ title }}
    </div>
    <el-skeleton animated :class="heightClass" :loading="props.loading">
      <template #template>
        <el-skeleton-item variant="rect" class="h-full rounded-[12px]" />
      </template>
      <template #default>
        <div
          v-if="items.length"
          class="px-[12px] py-[4px] rounded-[12px] bg-[rgba(28,28,30,0.06)] card-skeleton"
        >
          <div
            v-for="item in items"
            :key="item.name"
            class="flex justify-between items-center py-[8px] leading-[16px] text-[#1C1C1E] border-b border-[rgba(28,28,30,0.08)] last:border-b-0 box-border"
          >
            <div class="flex items-center gap-[4px]">
              <span class="font-regular text-[12px] leading-[16px]">{{ tt(item.name) }}</span>
              <span
                class="font-regular text-[12px] leading-[16px] text-[#6E6E73]"
                v-if="item.short_name"
                >{{ tt(item.short_name) }}</span
              >
            </div>
            <div class="flex items-center gap-[2px]">
              <Icon :name="item.trend === 1 ? 'up' : 'down'" v-if="item.trend && item.trend != 0" />
              <div class="flex items-end gap-[2px]">
                <span class="font-bold text-[13px] leading-[16px]">{{
                  item.value != null && item.value !== "" ? item.value : "-"
                }}</span>
                <span
                  v-if="item.unit"
                  class="font-medium text-[10px] leading-[14px] text-[rgba(28,28,30,.6)]"
                  >{{ tt(item.unit) }}</span
                >
              </div>
            </div>
          </div>
        </div>
        <div
          v-else
          class="flex items-center justify-center rounded-[12px]"
          :class="heightClass"
          style="background: rgba(28, 28, 30, 0.06)"
        >
          <DmpEmpty />
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<script setup lang="ts">
import { InfoDetailItem } from "@/infoDetailTable/props";
import { Icon } from "../icon/icon";
import { DmpEmpty } from "../empty";
import { computed, inject, Ref } from "vue";
import { TransferLang } from "../../../common/types";

const props = defineProps<{
  items: InfoDetailItem[];
  title?: string;
  loading?: boolean;
  heightClass?: string;
}>();
console.log("props", props.items);
const tt = inject<Ref<TransferLang>>("tt");
const heightClass = computed(() => {
  return props.heightClass ?? "h-[232px]";
});
</script>
