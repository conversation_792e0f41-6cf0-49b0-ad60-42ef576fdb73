/*
 * @Date: 2025-07-11 16:27:42
 * @LastEditors: 谢晓静
 * @LastEditTime: 2025-07-11 17:13:06
 * @FilePath: /MyBusinessV2/packages/components/src/infoDetailTable/index.ts
 */
import { withInstall } from "../utils/install.ts";

import InfoDetailTable from "./infoDetailTable.vue";

export const DmpInfoDetailTable = withInstall(InfoDetailTable);
export default DmpInfoDetailTable;

export * from "./infoDetailTable.vue";
export * from "./props.ts";
