<script setup lang="ts">
import { useTipCarousel } from "./useTipCarousel";
import { TipCarouselProps } from "./props";
import { inject, Ref } from "vue";
import { TransferLang } from "../../../common/types";

const tt = inject<Ref<TransferLang>>("tt");

const props = withDefaults(defineProps<TipCarouselProps>(), {
  tips: () => [],
  interval: 3000,
  loop: true,
  autoplay: true,
});

const { currentIndex } = useTipCarousel(props);
</script>
<template>
  <div class="font-medium leading-[16px] text-[12px] text-center w-full">
    <p
      :title="tt(props.tips[currentIndex])"
      :key="`tip-${currentIndex}-${props.tips[currentIndex]}`"
      class="truncate text-ellipsis overflow-hidden w-full"
    >
      {{ tt(props.tips[currentIndex]) }}
    </p>
  </div>
</template>
