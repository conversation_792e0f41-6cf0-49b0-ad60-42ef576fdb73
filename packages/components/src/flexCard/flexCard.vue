<script setup lang="ts">
import { Icon } from "@/icon/icon";
import { FlexCardProps } from "@/flexCard/props";
import { inject, Ref } from "vue";

defineOptions({ name: "DmpFlexCard" });
const {
  title,
  showArrow = true,
  gridSpan = {
    row: 1,
    col: 1,
  },
} = defineProps<FlexCardProps>();

const isMobile = inject<Ref<boolean>>("isMobile");
</script>

<template>
  <div
    class="dmp-flex-card flex flex-col gap-[8px]"
    :class="{ 'glass-card': isMobile }"
    :style="{
      gridRow: `span ${gridSpan.row}`,
      gridColumn: `span ${gridSpan.col}`,
    }"
  >
    <div class="title flex items-center" v-if="title">
      <span class="text-[13px] text-[#1c1c1e] font-medium leading-[20px]">{{ title }}</span>
      <slot name="titleExtra"></slot>
      <Icon v-if="showArrow" name="arrow-right" />
    </div>
    <div class="content h-full">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped></style>
