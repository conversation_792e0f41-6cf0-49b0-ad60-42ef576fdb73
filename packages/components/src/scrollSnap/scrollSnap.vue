<script setup lang="ts">
import { toRefs, withDefaults, ref } from "vue";
import { useScrollSnap } from "@/hooks/useScrollSnap";
import { ScrollSnapProps } from "./props";

defineOptions({ name: "DmpScrollSnap" });
const props = withDefaults(defineProps<ScrollSnapProps>(), {
  list: () => [],
  itemsPerScreen: 1,
  needCalcColumn: true,
  needIndicator: true,
});

const { list, itemsPerScreen, needCalcColumn, needIndicator } = toRefs(props);

const scrollContainer = ref<HTMLDivElement>(null);
const scrollItemRefs = ref<HTMLElement[]>([]);

const { groupList, currentScreen, setGroupWidth, setGroupCol, handleIndicatorClick } =
  useScrollSnap(list, itemsPerScreen, scrollContainer, scrollItemRefs);
</script>

<template>
  <div class="dmp-scroll-snap">
    <div class="scroll-container" ref="scrollContainer">
      <div
        class="screen-item relative"
        v-for="(group, groupIndex) in groupList"
        :key="groupIndex"
        :data-index="groupIndex"
        :style="setGroupWidth(groupIndex)"
        :ref="(el) => (scrollItemRefs[groupIndex] = el as HTMLElement)"
      >
        <div class="grid grid-card" :style="needCalcColumn ? setGroupCol(group.length) : ''">
          <template v-for="item in group" :key="item.name">
            <slot :item="item" :group="group" :group-index="groupIndex"></slot>
          </template>
        </div>
      </div>
    </div>

    <!-- 滚动指示器 -->
    <div
      class="progress-indicator absolute box-border bottom-[4px] right-[8px] px-[11px] h-[16px] rounded-[8px] bg-[rgba(28,28,30,.07)] flex items-center justify-between gap-[6px] cursor-pointer"
      @click="handleIndicatorClick"
      v-if="needIndicator && groupList.length > 1"
    >
      <div
        v-for="(_, index) in groupList.length"
        :key="index"
        class="h-[4px] rounded-[8px] bg-[rgba(28,28,30,0.24)] transition-all duration-200"
        :class="[currentScreen === index ? '!w-[16px] !bg-[rgba(28,28,30,0.64)]' : '!w-[4px]']"
      ></div>
    </div>
  </div>
</template>

<style scoped>
.dmp-scroll-snap {
  background-color: red;
}
.scroll-container {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

.screen-item {
  scroll-snap-align: start;
  flex-shrink: 0;
}

.progress-indicator {
  user-select: none;
}
</style>
