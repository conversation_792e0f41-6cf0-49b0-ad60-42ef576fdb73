/* 自定义进度条样式 */
.progress-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-top: 7px;
}
.progress-text {
  color: rgba(28, 28, 28, 0.56);
  font-size: 14px;
  font-weight: 600;
  text-align: left;
  display: inline-flex;
  align-items: flex-end;
  gap: 2px;
}
.progress-unit {
  color: rgba(28, 28, 28, 0.56);
  font-size: 11px;
  font-weight: 600;
}

.progress-track {
  position: relative;
  width: 100%;
  background: rgba(28, 28, 30, 0.06);
  overflow: hidden;
  flex-shrink: 0;
}

.progress-fill {
  position: relative;
  height: 100%;
  transition: width 0.3s ease;

  /* 进度条末端的小圆点 */
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    right: -6px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transform: translateY(-50%);
    border: 2px solid #ebebeb;
    transition: opacity 0.3s ease;
    background: var(--dot-color, rgba(28, 28, 30, 0.4));
  }

  /* 根据data属性控制小圆点显示 */
  &[data-show-dot="false"]::after {
    opacity: 0;
  }

  &[data-show-dot="true"]::after {
    opacity: 1;
  }
}
