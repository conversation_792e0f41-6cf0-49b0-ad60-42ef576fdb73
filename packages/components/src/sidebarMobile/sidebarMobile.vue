<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from "vue";
import { SidebarMobileProps, SidebarMobileEmits, MenuItem } from "./props.ts";

defineOptions({ name: "DmpSidebarMobile" });
const { menuItems = [], initPath, show } = defineProps<SidebarMobileProps>();
const emit = defineEmits<SidebarMobileEmits>();

// 当前激活的子菜单路径
const activeSubmenuPath = ref<string>("");

// 检查是否是当前激活的子菜单
function isSubmenuActive(submenu: MenuItem) {
  return submenu.path === activeSubmenuPath.value;
}

// 导航到子菜单
function navigateToSubmenu(submenu: MenuItem) {
  // 设置当前激活的子菜单
  activeSubmenuPath.value = submenu.path;

  emit("select", submenu);
}

// 监听路由变化，自动设置激活状态和展开父菜单
watch(
  () => initPath,
  (newPath) => {
    activeSubmenuPath.value = newPath;
  },
  { immediate: true }
);
</script>

<template>
  <aside
    :class="[
      'dmp-sidebar-mobile fixed left-0 top-0 w-full h-full flex flex-col transition-transform duration-300 z-[49]',
      show ? 'translate-x-0' : '-translate-x-full',
    ]"
  >
    <!-- 侧边栏头部 -->
    <div class="pl-[16px] pt-[8px] flex-shrink-0">
      <slot name="toogleIcon"></slot>
    </div>

    <!-- 菜单列表 -->
    <nav class="flex-1 overflow-y-auto pt-[20px]">
      <!-- 二级菜单 -->
      <ul class="mt-[2px] space-y-[1px] px-[16px]">
        <li v-for="item in menuItems" :key="item.title">
          <button
            @click="navigateToSubmenu(item)"
            :class="[
              'w-full text-left font-medium px-[16px] h-[56px] leading-[32px] rounded-[32px] text-【20px】 transition-colors',
              isSubmenuActive(item)
                ? 'bg-[rgba(0,113,227,0.08)] text-[#0071E3] font-medium'
                : 'text-[#1C1C1E] hover:text-[#0071E3] hover:bg-[rgba(0,113,227,0.08)]',
            ]"
          >
            {{ item.title }}
          </button>
        </li>
      </ul>
    </nav>

    <!-- 侧边栏底部 -->
    <!-- <div class="p-4 border-gray-100 flex-shrink-0">
      <div class="text-xs text-gray-500 text-center">
        {{ footer || "© 2024 MyBusiness" }}
      </div>
    </div> -->
  </aside>
</template>
