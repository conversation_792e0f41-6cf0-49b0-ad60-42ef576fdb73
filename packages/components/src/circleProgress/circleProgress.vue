<script setup lang="ts">
import { CircleProgressProps } from "@/circleProgress/props";
import { useCircleProgress } from "@/circleProgress/useCircleProgress";
import { inject, Ref } from "vue";
import { TransferLang } from "../../../common/types";

const tt = inject<Ref<TransferLang>>("tt");

defineOptions({ name: "DmpCircleProgress" });

const props = withDefaults(defineProps<CircleProgressProps>(), {
  progress: 0,
  label: "健康度",
  openDirection: "bottom",
  size: 90,
  strokeWidth: 8,
  backgroundColor: "rgba(28,28,30,0.06)",
  dotSize: 4,
  arcAngle: 280,
  strokeColor: () => ["#2EB972", "#9DE76A"],
});

const { backgroundPath, progressPath, progressDashArray, progressDashOffset, dotPosition } =
  useCircleProgress(props);
</script>

<template>
  <div class="arc-progress-container">
    <svg :width="props.size" :height="props.size" viewBox="0 0 100 100" class="arc-progress-svg">
      <!-- 定义渐变 -->
      <defs>
        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" :style="{ stopColor: props.strokeColor?.[0] || '#2EB972' }" />
          <stop offset="100%" :style="{ stopColor: props.strokeColor?.[1] || '#9DE76A' }" />
        </linearGradient>
      </defs>

      <!-- 背景弧线 -->
      <path
        :d="backgroundPath"
        fill="none"
        :stroke="props.backgroundColor"
        :stroke-width="props.strokeWidth"
        stroke-linecap="round"
      />

      <!-- 进度弧线 -->
      <path
        :d="progressPath"
        fill="none"
        stroke="url(#progressGradient)"
        :stroke-width="props.strokeWidth"
        stroke-linecap="round"
        :stroke-dasharray="progressDashArray"
        :stroke-dashoffset="progressDashOffset"
      />

      <!-- 圆点 -->
      <circle
        v-if="props.progress > 0"
        :cx="dotPosition.x"
        :cy="dotPosition.y"
        :r="props.dotSize / 2"
        fill="#fff"
        stroke="#FFFFFF"
        stroke-width="1"
      />
    </svg>

    <!-- 中间内容 -->
    <div class="arc-progress-content">
      <div class="progress-value">{{ Math.round(props.progress) }}</div>
      <div class="progress-label">{{ tt(props.label) }}</div>
    </div>
  </div>
</template>

<style scoped>
@import "./index.scss";
</style>
