import { inject, Ref } from "vue";
import { TransferLang } from "../types";

// 替换字符串中的想要多语言的词语 ('你好123世界456', ['你好', '世界'])  => 'hello123world456'

export const useLanguageReplace = () => {
  const tt = inject<Ref<TransferLang>>("tt");
  const transferLangByWords = (originStr: string, needReplaceWords: string[]): string => {
    if (!originStr || !needReplaceWords || needReplaceWords.length === 0) {
      return originStr;
    }
    let replacedStr = originStr;
    needReplaceWords.forEach((word) => {
      replacedStr = replacedStr.replace(new RegExp(word, "g"), tt.value ? tt.value(word) : word);
    });
    return replacedStr;
  };
  return { transferLangByWords };
};
